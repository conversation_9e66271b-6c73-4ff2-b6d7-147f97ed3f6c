-- Essential fixes for ride_requests table

-- Standardize location fields
ALTER TABLE IF EXISTS ride_requests
    ALTER COLUMN pickup_latitude TYPE DECIMAL(10, 8),
    ALTER COLUMN pickup_longitude TYPE DECIMAL(11, 8),
    ALTER COLUMN destination_latitude TYPE DECIMAL(10, 8),
    ALTER COLUMN destination_longitude TYPE DECIMAL(11, 8);

-- Add missing indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_ride_requests_user_id ON ride_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_ride_requests_driver_id ON ride_requests(driver_id);
CREATE INDEX IF NOT EXISTS idx_ride_requests_status ON ride_requests(status);
CREATE INDEX IF NOT EXISTS idx_ride_requests_pickup_coords ON ride_requests(pickup_latitude, pickup_longitude);

-- Enable RLS if not already enabled
ALTER TABLE ride_requests ENABLE ROW LEVEL SECURITY;

-- Basic security policies
CREATE POLICY IF NOT EXISTS "Users can view their own ride requests"
    ON ride_requests FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can create ride requests"
    ON ride_requests FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Drivers can view and update assigned rides"
    ON ride_requests FOR ALL
    USING (auth.uid() = driver_id);

-- Add basic validation trigger
CREATE OR REPLACE FUNCTION validate_ride_coordinates()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.pickup_latitude < -90 OR NEW.pickup_latitude > 90 OR
       NEW.destination_latitude < -90 OR NEW.destination_latitude > 90 OR
       NEW.pickup_longitude < -180 OR NEW.pickup_longitude > 180 OR
       NEW.destination_longitude < -180 OR NEW.destination_longitude > 180 THEN
        RAISE EXCEPTION 'Invalid coordinates';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER check_ride_coordinates
    BEFORE INSERT OR UPDATE ON ride_requests
    FOR EACH ROW
    EXECUTE FUNCTION validate_ride_coordinates();