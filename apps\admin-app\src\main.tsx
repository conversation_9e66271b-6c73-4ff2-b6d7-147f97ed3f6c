import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { supabase } from './lib/supabase'

// ⚡ CONFIGURAÇÃO OTIMIZADA - COMUNICAÇÃO SUPABASE PARA ADMIN APP
// Implementação com monitoramento avançado para o painel administrativo

// Configuração de monitoramento e recuperação para o Admin App
const setupAdminSupabaseConnection = () => {
  // Histórico de saúde da conexão
  const connectionHistory: Array<{
    timestamp: number;
    status: 'healthy' | 'degraded' | 'unhealthy' | 'offline';
    latency: number;
    message: string;
  }> = []
  
  // Histórico de erros para diagnóstico
  const errorHistory: Array<{
    timestamp: number;
    error: any;
    context: string;
  }> = []
  
  // Função para verificar saúde da conexão
  const checkConnectionHealth = async (): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy' | 'offline';
    latency: number;
    message: string;
  }> => {
    if (!navigator.onLine) {
      return {
        status: 'offline',
        latency: 0,
        message: 'Dispositivo offline'
      }
    }
    
    const startTime = Date.now()
    
    try {
      // Tentar acessar diferentes tabelas para um diagnóstico mais completo
      const [usersResult, ridesResult, driversResult] = await Promise.all([
        supabase.from('profiles').select('count').limit(1),
        supabase.from('ride_requests').select('count').limit(1),
        supabase.from('driver_locations').select('count').limit(1)
      ])
      
      const latency = Date.now() - startTime
      
      // Verificar se houve erro em alguma consulta
      if (usersResult.error || ridesResult.error || driversResult.error) {
        // Problemas parciais
        return {
          status: 'degraded',
          latency,
          message: 'Acesso parcial ao banco de dados'
        }
      }
      
      // Verificar latência
      if (latency > 1500) {
        return {
          status: 'degraded',
          latency,
          message: `Latência alta (${latency}ms)`
        }
      }
      
      // Tudo OK
      return {
        status: 'healthy',
        latency,
        message: `Conexão saudável (${latency}ms)`
      }
    } catch (error) {
      const latency = Date.now() - startTime
      
      // Registrar erro
      errorHistory.push({
        timestamp: Date.now(),
        error,
        context: 'health_check'
      })
      
      return {
        status: 'unhealthy',
        latency,
        message: `Erro na conexão: ${error.message || 'Erro desconhecido'}`
      }
    }
  }
  
  // Função para registrar verificação de saúde
  const registerHealthCheck = (check: {
    status: 'healthy' | 'degraded' | 'unhealthy' | 'offline';
    latency: number;
    message: string;
  }) => {
    // Adicionar ao histórico
    connectionHistory.push({
      timestamp: Date.now(),
      ...check
    })
    
    // Limitar tamanho do histórico
    if (connectionHistory.length > 50) {
      connectionHistory.shift()
    }
    
    // Salvar no localStorage para dashboard de monitoramento
    try {
      localStorage.setItem('connection_history', JSON.stringify(connectionHistory))
    } catch (e) {
      console.error('❌ Erro ao salvar histórico de conexão:', e)
    }
    
    // Log com estilo
    const logStyles = {
      healthy: 'color: green; font-weight: bold',
      degraded: 'color: orange; font-weight: bold',
      unhealthy: 'color: red; font-weight: bold',
      offline: 'color: gray; font-weight: bold'
    }
    
    console.log(
      `%c${check.status.toUpperCase()}%c ${check.message}`,
      logStyles[check.status],
      'color: inherit'
    )
    
    // Exibir indicador visual em desenvolvimento
    if (import.meta.env.DEV) {
      const statusColors = {
        healthy: '#4caf50',
        degraded: '#ff9800',
        unhealthy: '#f44336',
        offline: '#9e9e9e'
      }
      
      let indicator = document.getElementById('connection-status-indicator')
      
      if (!indicator) {
        indicator = document.createElement('div')
        indicator.id = 'connection-status-indicator'
        indicator.style.position = 'fixed'
        indicator.style.top = '10px'
        indicator.style.right = '10px'
        indicator.style.padding = '8px 12px'
        indicator.style.borderRadius = '4px'
        indicator.style.fontSize = '12px'
        indicator.style.fontWeight = 'bold'
        indicator.style.zIndex = '9999'
        document.body.appendChild(indicator)
      }
      
      indicator.textContent = `${check.status.toUpperCase()}: ${check.latency}ms`
      indicator.style.backgroundColor = statusColors[check.status]
      indicator.style.color = check.status === 'healthy' ? '#fff' : '#fff'
    }
  }
  
  // Tratamento de erros global melhorado
  const setupErrorHandling = () => {
    if (typeof window === 'undefined') return
    
    // Interceptar erros de rede
    const originalFetch = window.fetch
    window.fetch = async (input, init) => {
      try {
        const response = await originalFetch(input, init)
        
        // Verificar se é uma requisição do Supabase
        const url = typeof input === 'string' ? input : input.url
        if (url.includes('supabase')) {
          if (!response.ok) {
            // Registrar erro
            errorHistory.push({
              timestamp: Date.now(),
              error: {
                status: response.status,
                statusText: response.statusText,
                url
              },
              context: 'fetch'
            })
            
            // Salvar histórico de erros
            try {
              localStorage.setItem('error_history', JSON.stringify(
                errorHistory.slice(-50)
              ))
            } catch (e) {
              console.error('❌ Erro ao salvar histórico de erros:', e)
            }
            
            console.warn(`⚠️ Erro HTTP ${response.status}: ${response.statusText}`, { url })
          }
        }
        
        return response
      } catch (error) {
        // Verificar se é uma requisição do Supabase
        const url = typeof input === 'string' ? input : input.url
        if (url.includes('supabase')) {
          // Registrar erro
          errorHistory.push({
            timestamp: Date.now(),
            error,
            context: 'fetch'
          })
          
          console.error(`❌ Erro de rede:`, error)
        }
        
        throw error
      }
    }
    
    // Interceptar erros globais
    window.addEventListener('error', (event) => {
      if (event.message?.includes('supabase') || 
          event.filename?.includes('supabase')) {
        // Registrar erro
        errorHistory.push({
          timestamp: Date.now(),
          error: {
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno
          },
          context: 'global'
        })
        
        console.error('❌ Erro Supabase detectado:', event)
      }
    })
    
    // Interceptar rejeições de promessas não tratadas
    window.addEventListener('unhandledrejection', (event) => {
      const error = event.reason
      if (error?.message?.includes('supabase') || 
          error?.stack?.includes('supabase')) {
        // Registrar erro
        errorHistory.push({
          timestamp: Date.now(),
          error,
          context: 'promise'
        })
        
        console.error('❌ Promessa não tratada relacionada ao Supabase:', error)
      }
    })
  }
  
  // Inicializar monitor de saúde
  const initializeHealthMonitor = () => {
    // Verificar saúde inicial
    checkConnectionHealth().then(registerHealthCheck)
    
    // Configurar verificação periódica
    setInterval(async () => {
      const health = await checkConnectionHealth()
      registerHealthCheck(health)
      
      // Tentar reconectar realtime se necessário
      if (health.status === 'unhealthy' || health.status === 'degraded') {
        if (navigator.onLine && supabase.realtime) {
          try {
            await supabase.realtime.connect()
            console.log('🔄 Reconexão Realtime iniciada')
          } catch (e) {
            console.error('❌ Erro ao reconectar Realtime:', e)
          }
        }
      }
    }, 30000) // A cada 30 segundos
    
    // Monitorar eventos de conexão
    if (typeof window !== 'undefined') {
      window.addEventListener('online', async () => {
        console.log('🌐 Conexão de internet restaurada')
        
        // Verificar saúde imediatamente
        const health = await checkConnectionHealth()
        registerHealthCheck(health)
        
        // Tentar reconectar realtime
        if (supabase.realtime) {
          try {
            await supabase.realtime.connect()
            console.log('✅ Conexão Realtime restabelecida')
          } catch (e) {
            console.error('❌ Erro ao reconectar Realtime:', e)
          }
        }
      })
      
      window.addEventListener('offline', () => {
        console.log('🔌 Conexão de internet perdida')
        registerHealthCheck({
          status: 'offline',
          latency: 0,
          message: 'Dispositivo offline'
        })
      })
    }
  }
  
  // Configurar tratamento de erros
  setupErrorHandling()
  
  // Inicializar monitor de saúde
  initializeHealthMonitor()
  
  // Carregar histórico do localStorage
  try {
    const savedHistory = localStorage.getItem('connection_history')
    if (savedHistory) {
      const parsed = JSON.parse(savedHistory)
      if (Array.isArray(parsed)) {
        connectionHistory.push(...parsed)
      }
    }
    
    const savedErrors = localStorage.getItem('error_history')
    if (savedErrors) {
      const parsed = JSON.parse(savedErrors)
      if (Array.isArray(parsed)) {
        errorHistory.push(...parsed)
      }
    }
  } catch (e) {
    console.error('❌ Erro ao carregar histórico:', e)
  }
  
  // Retornar API do monitor
  return {
    checkHealth: checkConnectionHealth,
    getConnectionHistory: () => [...connectionHistory],
    getErrorHistory: () => [...errorHistory],
    clearHistory: () => {
      connectionHistory.length = 0
      errorHistory.length = 0
      localStorage.removeItem('connection_history')
      localStorage.removeItem('error_history')
    }
  }
}

// Render app immediately
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)

// Inicializar serviços do Admin App
const initializeAdminServices = async () => {
  try {
    // Configurar monitor avançado de conexão
    const connectionMonitor = setupAdminSupabaseConnection()
    
    // Disponibilizar globalmente para debugging e uso no painel
    if (typeof window !== 'undefined') {
      (window as any).__connectionMonitor = connectionMonitor
    }
    
    // Adicionar botão de diagnóstico em desenvolvimento
    if (import.meta.env.DEV) {
      const diagButton = document.createElement('button')
      diagButton.textContent = 'Diagnóstico DB'
      diagButton.style.position = 'fixed'
      diagButton.style.bottom = '10px'
      diagButton.style.left = '10px'
      diagButton.style.padding = '8px 12px'
      diagButton.style.backgroundColor = '#2196f3'
      diagButton.style.color = 'white'
      diagButton.style.border = 'none'
      diagButton.style.borderRadius = '4px'
      diagButton.style.cursor = 'pointer'
      diagButton.style.zIndex = '9999'
      
      diagButton.addEventListener('click', async () => {
        const health = await connectionMonitor.checkHealth()
        alert(`Status: ${health.status}\nLatência: ${health.latency}ms\nMensagem: ${health.message}`)
      })
      
      document.body.appendChild(diagButton)
    }

    console.log('✅ Serviços do Admin App inicializados com sucesso')

  } catch (error) {
    console.warn('⚠️ Falha na inicialização dos serviços:', error)
    
    // Interface de recuperação para erros críticos
    if (typeof document !== 'undefined') {
      const recoveryDiv = document.createElement('div')
      recoveryDiv.style.position = 'fixed'
      recoveryDiv.style.top = '0'
      recoveryDiv.style.left = '0'
      recoveryDiv.style.width = '100%'
      recoveryDiv.style.padding = '10px'
      recoveryDiv.style.backgroundColor = '#f44336'
      recoveryDiv.style.color = 'white'
      recoveryDiv.style.textAlign = 'center'
      recoveryDiv.style.zIndex = '9999'
      recoveryDiv.innerHTML = 'Erro crítico de inicialização. <a href="javascript:location.reload()" style="color:white;text-decoration:underline">Recarregar</a>'
      document.body.appendChild(recoveryDiv)
    }
  }
}

// Inicializar serviços
initializeAdminServices()
