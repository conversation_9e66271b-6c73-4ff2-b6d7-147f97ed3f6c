import { useState, useEffect, useRef, useCallback } from 'react';
import { supabase } from '../services/supabase-client';
import { supabaseErrorHandler } from '../services/supabase-error-handler';

// Tipos de estado de consulta
export type QueryStatus = 'idle' | 'loading' | 'success' | 'error';

// Opções para a consulta
export interface QueryOptions {
  enabled?: boolean;
  refetchInterval?: number | false;
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
  retryCount?: number;
  retryDelay?: number;
}

// Tipos de consulta do Supabase
export type QueryType = 'select' | 'insert' | 'update' | 'delete' | 'upsert';

// Resultado da consulta
export interface QueryResult<T = any> {
  data: T | null;
  error: any;
  status: QueryStatus;
  isLoading: boolean;
  isSuccess: boolean;
  isError: boolean;
  refetch: () => Promise<void>;
  mutate: (newData: any) => void;
}

/**
 * Hook para realizar consultas no Supabase com retry e fallback automáticos
 */
export function useSupabaseQuery<T = any>(
  queryType: QueryType,
  table: string,
  queryParams: any = {},
  options: QueryOptions = {}
): QueryResult<T> {
  // Opções padrão
  const {
    enabled = true,
    refetchInterval = false,
    onSuccess,
    onError,
    retryCount = 3,
    retryDelay = 1000
  } = options;

  // Estado da consulta
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<any>(null);
  const [status, setStatus] = useState<QueryStatus>('idle');
  
  // Referências para controle
  const retryCountRef = useRef(0);
  const refetchIntervalRef = useRef<any>(null);
  const isMounted = useRef(true);
  const lastFetchTime = useRef<number>(0);
  
  // Parâmetros da consulta memoizados
  const memoizedParams = useRef(queryParams);
  
  // Atualizar parâmetros da consulta quando mudam
  useEffect(() => {
    memoizedParams.current = queryParams;
    
    // Refazer consulta quando os parâmetros mudam
    if (status !== 'idle' && enabled) {
      executeQuery();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(queryParams), queryType, table, enabled]);
  
  // Limpar na desmontagem
  useEffect(() => {
    return () => {
      isMounted.current = false;
      if (refetchIntervalRef.current) {
        clearInterval(refetchIntervalRef.current);
      }
    };
  }, []);
  
  // Executar consulta com tratamento de erros
  const executeQuery = useCallback(async (): Promise<void> => {
    // Verificar se deve executar
    if (!enabled || !isMounted.current) {
      return;
    }
    
    // Definir estado de carregamento
    if (status !== 'loading') {
      setStatus('loading');
    }
    
    // Registrar tempo de início
    lastFetchTime.current = Date.now();
    
    try {
      let result;
      
      // Executar consulta apropriada
      switch (queryType) {
        case 'select':
          result = await supabase
            .from(table)
            .select(memoizedParams.current.select || '*')
            .order(memoizedParams.current.orderBy || 'created_at', { ascending: memoizedParams.current.ascending ?? false })
            .range(memoizedParams.current.from || 0, memoizedParams.current.to || 9)
            .eq(memoizedParams.current.eq?.column || '', memoizedParams.current.eq?.value || '')
            .in(memoizedParams.current.in?.column || '', memoizedParams.current.in?.values || [])
            .limit(memoizedParams.current.limit || 100);
          break;
          
        case 'insert':
          result = await supabase
            .from(table)
            .insert(memoizedParams.current.data || {}, { returning: 'representation' });
          break;
          
        case 'update':
          result = await supabase
            .from(table)
            .update(memoizedParams.current.data || {}, { returning: 'representation' })
            .match(memoizedParams.current.match || {});
          break;
          
        case 'delete':
          result = await supabase
            .from(table)
            .delete({ returning: 'representation' })
            .match(memoizedParams.current.match || {});
          break;
          
        case 'upsert':
          result = await supabase
            .from(table)
            .upsert(memoizedParams.current.data || {}, { 
              returning: 'representation',
              onConflict: memoizedParams.current.onConflict
            });
          break;
          
        default:
          throw new Error(`Tipo de consulta não suportado: ${queryType}`);
      }
      
      // Verificar se houve erro
      if (result.error) {
        throw result.error;
      }
      
      // Definir dados e estado de sucesso
      if (isMounted.current) {
        setData(result.data as T);
        setError(null);
        setStatus('success');
        
        // Chamar callback de sucesso
        if (onSuccess) {
          onSuccess(result.data);
        }
      }
      
      // Resetar contador de tentativas
      retryCountRef.current = 0;
    } catch (err) {
      // Registrar erro
      supabaseErrorHandler.handleError(err, 'database');
      
      // Definir erro e estado de erro
      if (isMounted.current) {
        setError(err);
        setStatus('error');
        
        // Chamar callback de erro
        if (onError) {
          onError(err);
        }
      }
      
      // Tentar novamente se não excedeu o número máximo de tentativas
      if (retryCountRef.current < retryCount) {
        retryCountRef.current++;
        
        // Calcular delay com backoff exponencial
        const delay = retryDelay * Math.pow(2, retryCountRef.current - 1);
        
        // Agendar nova tentativa
        setTimeout(() => {
          if (isMounted.current) {
            executeQuery();
          }
        }, delay);
      }
    }
  }, [queryType, table, onSuccess, onError, retryCount, retryDelay, status, enabled]);
  
  // Função para recarregar dados
  const refetch = useCallback(async (): Promise<void> => {
    // Verificar throttling (evitar muitas requisições consecutivas)
    const now = Date.now();
    const timeSinceLastFetch = now - lastFetchTime.current;
    
    if (timeSinceLastFetch < 200) {
      // Aguardar um pouco antes de refazer a consulta
      await new Promise(resolve => setTimeout(resolve, 200 - timeSinceLastFetch));
    }
    
    // Resetar contador de tentativas
    retryCountRef.current = 0;
    
    // Executar consulta
    executeQuery();
  }, [executeQuery]);
  
  // Função para mutar dados localmente
  const mutate = useCallback((newData: any): void => {
    if (isMounted.current) {
      setData(newData as T);
    }
  }, []);
  
  // Executar consulta inicial
  useEffect(() => {
    if (enabled) {
      executeQuery();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [enabled]);
  
  // Configurar intervalo de recarregamento
  useEffect(() => {
    // Limpar intervalo existente
    if (refetchIntervalRef.current) {
      clearInterval(refetchIntervalRef.current);
      refetchIntervalRef.current = null;
    }
    
    // Configurar novo intervalo
    if (refetchInterval && typeof refetchInterval === 'number' && refetchInterval > 0) {
      refetchIntervalRef.current = setInterval(() => {
        if (isMounted.current && enabled) {
          refetch();
        }
      }, refetchInterval);
    }
    
    return () => {
      if (refetchIntervalRef.current) {
        clearInterval(refetchIntervalRef.current);
      }
    };
  }, [refetchInterval, enabled, refetch]);
  
  return {
    data,
    error,
    status,
    isLoading: status === 'loading',
    isSuccess: status === 'success',
    isError: status === 'error',
    refetch,
    mutate
  };
}

/**
 * Hook para consultar dados de uma tabela
 */
export function useSupabaseSelect<T = any>(
  table: string,
  params: {
    select?: string;
    eq?: { column: string; value: any };
    in?: { column: string; values: any[] };
    orderBy?: string;
    ascending?: boolean;
    limit?: number;
    from?: number;
    to?: number;
  } = {},
  options: QueryOptions = {}
): QueryResult<T> {
  return useSupabaseQuery<T>('select', table, params, options);
}

/**
 * Hook para inserir dados em uma tabela
 */
export function useSupabaseInsert<T = any>(
  table: string,
  options: QueryOptions = {}
): {
  result: QueryResult<T>;
  insert: (data: any) => Promise<T | null>;
} {
  const [queryParams, setQueryParams] = useState<any>({ data: null });
  
  const result = useSupabaseQuery<T>(
    'insert',
    table,
    queryParams,
    { ...options, enabled: false }
  );
  
  const insert = useCallback(
    async (data: any): Promise<T | null> => {
      setQueryParams({ data });
      await result.refetch();
      return result.data;
    },
    [result]
  );
  
  return { result, insert };
}

/**
 * Hook para atualizar dados em uma tabela
 */
export function useSupabaseUpdate<T = any>(
  table: string,
  options: QueryOptions = {}
): {
  result: QueryResult<T>;
  update: (data: any, match: any) => Promise<T | null>;
} {
  const [queryParams, setQueryParams] = useState<any>({ data: null, match: null });
  
  const result = useSupabaseQuery<T>(
    'update',
    table,
    queryParams,
    { ...options, enabled: false }
  );
  
  const update = useCallback(
    async (data: any, match: any): Promise<T | null> => {
      setQueryParams({ data, match });
      await result.refetch();
      return result.data;
    },
    [result]
  );
  
  return { result, update };
}

export default useSupabaseQuery;