# MobDrive - Sistema de Gestão de Mobilidade

Sistema completo de gestão para serviços de mobilidade urbana, incluindo aplicações para passageiros, motoristas e administradores.

## ✨ Melhorias Recentes (v2.0.0)

### 🔧 Sistema Supabase Otimizado
- ✅ **Removidas credenciais hardcoded** - Agora usa apenas variáveis de ambiente
- ✅ **Configuração centralizada** - Um único ponto de configuração para todos os apps
- ✅ **Código simplificado** - Removidos scripts complexos desnecessários
- ✅ **Melhor segurança** - Configurações seguras por padrão
- ✅ **Performance otimizada** - Conexões mais eficientes

### 🗄️ Database Limpo
- ✅ **Migrações simplificadas** - Removidos arquivos duplicados
- ✅ **Estrutura padronizada** - Nomes de tabelas consistentes
- ✅ **Scripts otimizados** - Instalação mais rápida e confiável

## 🚀 Estrutura do Projeto

```
MobDrive/
├── apps/
│   ├── admin-app/      # Aplicação administrativa
│   ├── driver-app/     # Aplicação para motoristas
│   ├── passenger-app/  # Aplicação para passageiros
│   └── shared/         # Componentes e utilitários compartilhados
├── database/           # Scripts e migrações do banco de dados
├── scripts/            # Scripts de monitoramento e teste
└── supabase/          # Configurações do Supabase
```

## 🛠️ Tecnologias

- **Frontend**: React/Next.js
- **Backend**: Supabase
- **Database**: PostgreSQL
- **Authentication**: Supabase Auth
- **Maps**: Mapbox
- **Monitoring**: Scripts customizados

## 📱 Aplicações

### Admin App
Interface administrativa para gerenciar motoristas, passageiros, corridas e relatórios.

### Driver App
Aplicação móvel/web para motoristas gerenciarem suas corridas e perfil.

### Passenger App
Aplicação para passageiros solicitarem corridas e acompanharem o serviço.

## 🚀 Como Executar

### 1. Clone o repositório
```bash
git clone https://github.com/jeremias210421/mobdrive.git
cd mobdrive
```

### 2. Instale as dependências
```bash
npm install
```

### 3. Configure as variáveis de ambiente
```bash
cp .env.example .env
```

**⚠️ IMPORTANTE**: Edite o arquivo `.env` e configure suas credenciais do Supabase:
- `VITE_SUPABASE_URL` - URL do seu projeto Supabase
- `VITE_SUPABASE_ANON_KEY` - Chave anônima do Supabase
- `VITE_MAPBOX_ACCESS_TOKEN` - Token do Mapbox para mapas

### 4. Teste a conexão com Supabase
```bash
npm run test:connection
```

### 5. Execute o projeto
```bash
# Para desenvolvimento geral
npm run dev

# Para apps específicos
cd apps/passenger-app && npm run dev
cd apps/driver-app && npm run dev
cd apps/admin-app && npm run dev
```

### 6. Monitor Supabase (opcional)
```bash
npm run monitor
```
Acesse http://localhost:3001 para ver o dashboard de monitoramento.

## 🔧 Scripts Disponíveis

- `npm run test:connection` - Testa conexão com Supabase
- `npm run monitor` - Inicia monitor de saúde do Supabase
- `npm run db:install` - Instala schema do banco de dados
- `npm run clean` - Limpa node_modules

## 🔒 Segurança

- ✅ Sem credenciais hardcoded no código
- ✅ Variáveis de ambiente obrigatórias
- ✅ RLS (Row Level Security) habilitado
- ✅ Políticas de acesso configuradas

## 📄 Licença

Este projeto está sob a licença MIT.

## 👥 Contribuição

Contribuições são bem-vindas! Por favor, abra uma issue ou pull request.

## 🆘 Suporte

Se encontrar problemas:
1. Verifique se as variáveis de ambiente estão configuradas
2. Execute `npm run test:connection` para testar Supabase
3. Consulte os logs do monitor em http://localhost:3001
4. Abra uma issue no GitHub
