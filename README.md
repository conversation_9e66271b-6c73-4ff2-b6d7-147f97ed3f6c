# MobDrive - Sistema de Gestão de Mobilidade

Sistema completo de gestão para serviços de mobilidade urbana, incluindo aplicações para passageiros, motoristas e administradores.

## 🚀 Estrutura do Projeto

```
MobDrive/
├── apps/
│   ├── admin-app/      # Aplicação administrativa
│   ├── driver-app/     # Aplicação para motoristas
│   ├── passenger-app/  # Aplicação para passageiros
│   └── shared/         # Componentes e utilitários compartilhados
├── database/           # Scripts e migrações do banco de dados
└── supabase/          # Configurações do Supabase
```

## 🛠️ Tecnologias

- **Frontend**: React/Next.js
- **Backend**: Supabase
- **Database**: PostgreSQL
- **Authentication**: Supabase Auth

## 📱 Aplicações

### Admin App
Interface administrativa para gerenciar motoristas, passageiros, corridas e relatórios.

### Driver App
Aplicação móvel/web para motoristas gerenciarem suas corridas e perfil.

### Passenger App
Aplicação para passageiros solicitarem corridas e acompanharem o serviço.

## 🚀 Como Executar

1. Clone o repositório
```bash
git clone https://github.com/jeremias210421/mobdrive.git
cd mobdrive
```

2. Instale as dependências
```bash
npm install
```

3. Configure as variáveis de ambiente
```bash
cp .env.example .env.local
```

4. Execute o projeto
```bash
npm run dev
```

## 📄 Licença

Este projeto está sob a licença MIT.

## 👥 Contribuição

Contribuições são bem-vindas! Por favor, abra uma issue ou pull request.
# MobDrive
# MobDrive
