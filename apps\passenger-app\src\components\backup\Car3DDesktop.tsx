// 📦 BACKUP - VERSÃO DESKTOP DE ALTA QUALIDADE
// Este arquivo foi movido para backup pois o projeto agora é mobile-only
// Para restaurar: copie de volta para src/components/

import React, { useRef, useState, useEffect, Suspense } from 'react'
import { Canvas, useFrame, useLoader } from '@react-three/fiber'
import { Environment, useTexture } from '@react-three/drei'
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';
import { TextureLoader } from 'three'
import * as THREE from 'three'

// 🖥️ VERSÃO DESKTOP DE ALTA QUALIDADE (BACKUP)
// - Materiais físicos realistas (MeshPhysicalMaterial)
// - Texturas de alta resolução
// - Iluminação avançada com HDR
// - Controles completos mouse + teclado
// - Sombras 4K e reflexões profissionais

// Componente otimizado que carrega apenas as texturas necessárias
const DesktopOBJCar: React.FC<{
  manualRotation: [number, number, number]
  textureMode: 'shaded' | 'pbr'
  textureConfig: number
  realisticMaterial: boolean
}> = ({ manualRotation, textureMode, textureConfig, realisticMaterial }) => {
  const carRef = useRef<THREE.Group>(null)

  // Carregar o modelo .obj
  const carModel = useLoader(OBJLoader, '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Shaded/base.obj')

  // Carregar texturas condicionalmente para otimizar performance
  const shadedTexture = textureMode === 'shaded'
    ? useLoader(TextureLoader, '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Shaded/shaded.png')
    : null

  const pbrTextures = textureMode === 'pbr'
    ? useTexture({
        diffuse: '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pbr/texture_diffuse.png',
        metallic: '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pbr/texture_metallic.png',
        normal: '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pbr/texture_normal.png',
        roughness: '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pbr/texture_roughness.png'
      })
    : null

  useFrame((state) => {
    if (carRef.current) {
      const scrollY = window.scrollY || 0
      const maxScroll = Math.max(document.body.scrollHeight - window.innerHeight, 1)
      const scrollProgress = Math.min(scrollY / maxScroll, 1)

      carRef.current.rotation.x = manualRotation[0] + Math.sin(scrollProgress * Math.PI) * 0.1
      carRef.current.rotation.y = manualRotation[1] + scrollProgress * Math.PI * 2 + state.clock.elapsedTime * 0.02
      carRef.current.rotation.z = manualRotation[2]
      carRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  // Aplicar texturas ao modelo
  useEffect(() => {
    if (carModel && (shadedTexture || pbrTextures)) {
      console.log('🖥️ Aplicando materiais desktop de alta qualidade...')
      console.log('📸 Modo de textura:', textureMode)
      if (shadedTexture) console.log('📸 Textura shaded carregada:', shadedTexture)
      if (pbrTextures) console.log('📸 Texturas PBR carregadas:', pbrTextures)

      // Configurar a textura shaded - Config 2 (Flip V) é a correta
      if (textureMode === 'shaded' && shadedTexture) {
        shadedTexture.wrapS = THREE.RepeatWrapping
        shadedTexture.wrapT = THREE.RepeatWrapping

        switch (textureConfig) {
          case 0: // Configuração original
            shadedTexture.flipY = false
            shadedTexture.repeat.set(1, 1)
            shadedTexture.offset.set(0, 0)
            break
          case 1: // Espelhar horizontalmente
            shadedTexture.flipY = false
            shadedTexture.repeat.set(-1, 1)
            shadedTexture.offset.set(1, 0)
            break
          case 2: // Espelhar verticalmente (CORRETO)
            shadedTexture.flipY = false
            shadedTexture.repeat.set(1, -1)
            shadedTexture.offset.set(0, 1)
            break
          case 3: // Espelhar ambos
            shadedTexture.flipY = false
            shadedTexture.repeat.set(-1, -1)
            shadedTexture.offset.set(1, 1)
            break
        }
      }

      carModel.traverse((child: THREE.Object3D) => {
        if (child instanceof THREE.Mesh) {
          const meshName = child.name.toLowerCase()

          if (textureMode === 'shaded' && shadedTexture) {
            if (realisticMaterial) {
              // Material físico realista para desktop
              if (meshName.includes('glass') || meshName.includes('window')) {
                // Vidros com material físico avançado
                child.material = new THREE.MeshPhysicalMaterial({
                  map: shadedTexture,
                  transparent: true,
                  opacity: 0.1,
                  transmission: 0.95,
                  thickness: 0.5,
                  roughness: 0.0,
                  metalness: 0.0,
                  clearcoat: 1.0,
                  clearcoatRoughness: 0.0,
                  ior: 1.5,
                })
              } else {
                // Carroceria com efeito de tinta automotiva premium
                child.material = new THREE.MeshPhysicalMaterial({
                  map: shadedTexture,
                  metalness: 0.95,        // Mais metálico
                  roughness: 0.05,        // Mais polido
                  clearcoat: 1.0,         // Verniz automotivo completo
                  clearcoatRoughness: 0.02, // Verniz espelhado
                  envMapIntensity: 2.0,   // Reflexões mais intensas
                  iridescence: 0.15,      // Efeito metálico mais visível
                  iridescenceIOR: 1.3,
                  sheen: 0.1,             // Brilho adicional
                })
              }
            } else {
              // Material padrão
              child.material = new THREE.MeshStandardMaterial({
                map: shadedTexture,
                metalness: 0.2,
                roughness: 0.8,
              })
            }
          } else if (textureMode === 'pbr' && pbrTextures) {
            // Texturas PBR com material físico premium
            child.material = new THREE.MeshPhysicalMaterial({
              map: pbrTextures.diffuse,
              metalnessMap: pbrTextures.metallic,
              normalMap: pbrTextures.normal,
              roughnessMap: pbrTextures.roughness,
              metalness: 1.0,
              roughness: 0.05,
              clearcoat: 0.9,
              clearcoatRoughness: 0.05,
              envMapIntensity: 2.5,
              normalScale: new THREE.Vector2(1.5, 1.5), // Normal map mais intenso
            })
          } else {
            // Fallback: Material físico básico
            child.material = new THREE.MeshPhysicalMaterial({
              color: '#1a1a1a',
              metalness: 0.9,
              roughness: 0.1,
              clearcoat: 0.8,
              clearcoatRoughness: 0.1,
              envMapIntensity: 1.5,
            })
          }

          child.castShadow = true
          child.receiveShadow = true
        }
      })
    }
  }, [carModel, shadedTexture, pbrTextures, textureMode, textureConfig, realisticMaterial])

  return (
    <group ref={carRef} position={[0, 0, 0]} scale={[2, 2, 2]}>
      {/* Rotação inicial para corrigir orientação da textura */}
      <group rotation={[0, Math.PI, 0]}>
        <primitive object={carModel} />
      </group>
    </group>
  )
}

// Componente principal desktop
interface Car3DDesktopProps {
  className?: string
}

export const Car3DDesktop: React.FC<Car3DDesktopProps> = ({ className = "" }) => {
  const [rotation, setRotation] = useState<[number, number, number]>([0, 0, 0])
  const [isDragging, setIsDragging] = useState(false)
  const [lastPosition, setLastPosition] = useState({ x: 0, y: 0 })
  const [useOBJ, setUseOBJ] = useState(true)
  const [textureMode, setTextureMode] = useState<'shaded' | 'pbr'>('shaded')
  const [textureConfig, setTextureConfig] = useState(2) // Config 2 (Flip V) é a correta
  const [realisticMaterial, setRealisticMaterial] = useState(true) // Material físico realista por padrão

  // Handlers para mouse (desktop)
  const handleMouseDown = (event: React.MouseEvent) => {
    event.preventDefault()
    setIsDragging(true)
    setLastPosition({ x: event.clientX, y: event.clientY })
  }

  const handleMouseMove = (event: React.MouseEvent) => {
    if (!isDragging) return
    event.preventDefault()

    const deltaX = event.clientX - lastPosition.x
    const deltaY = event.clientY - lastPosition.y

    setRotation(prev => [
      prev[0] + deltaY * 0.005,
      prev[1] + deltaX * 0.005,
      prev[2]
    ])

    setLastPosition({ x: event.clientX, y: event.clientY })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  // Event listeners globais
  useEffect(() => {
    const handleGlobalMouseUp = () => setIsDragging(false)
    const handleGlobalMouseMove = (event: MouseEvent) => {
      if (!isDragging) return

      const deltaX = event.clientX - lastPosition.x
      const deltaY = event.clientY - lastPosition.y

      setRotation(prev => [
        prev[0] + deltaY * 0.005,
        prev[1] + deltaX * 0.005,
        prev[2]
      ])

      setLastPosition({ x: event.clientX, y: event.clientY })
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove)
      document.addEventListener('mouseup', handleGlobalMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
    }
  }, [isDragging, lastPosition])

  // Controles de teclado (desktop)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const rotationSpeed = 0.1
      switch (event.key) {
        case 'ArrowLeft':
          setRotation(prev => [prev[0], prev[1] - rotationSpeed, prev[2]])
          break
        case 'ArrowRight':
          setRotation(prev => [prev[0], prev[1] + rotationSpeed, prev[2]])
          break
        case 'ArrowUp':
          setRotation(prev => [prev[0] - rotationSpeed, prev[1], prev[2]])
          break
        case 'ArrowDown':
          setRotation(prev => [prev[0] + rotationSpeed, prev[1], prev[2]])
          break
        case 'r':
        case 'R':
          setRotation([0, 0, 0])
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  return (
    <div 
      className={`relative w-full h-full ${className}`}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
    >
      <Canvas
        shadows
        camera={{ position: [6, 4, 6], fov: 50 }}
        style={{
          background: 'transparent',
          width: '100%',
          height: '100%'
        }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
        dpr={[1, 2]}
      >
        {/* Iluminação premium para desktop */}
        <ambientLight intensity={0.2} />
        
        {/* Luz principal ultra intensa para clearcoat */}
        <directionalLight
          position={[10, 10, 5]}
          intensity={3.0}
          castShadow
        />
        
        {/* Sistema de luzes profissional */}
        <pointLight position={[-10, 8, -10]} intensity={1.2} color="#ffffff" />
        <pointLight position={[10, 8, 10]} intensity={1.2} color="#ffffff" />
        <pointLight position={[0, 12, 0]} intensity={0.8} color="#f0f8ff" />
        <spotLight position={[5, 10, 5]} intensity={2} angle={0.3} penumbra={0.5} />

        {/* Ambiente HDR premium para reflexões ultra realistas */}
        <Environment preset="studio" background={false} />

        {/* Carro com Suspense */}
        <Suspense fallback={null}>
          {useOBJ ? (
            <DesktopOBJCar 
              manualRotation={rotation} 
              textureMode={textureMode} 
              textureConfig={textureConfig} 
              realisticMaterial={realisticMaterial} 
            />
          ) : (
            <group>
              {/* Fallback desktop seria aqui */}
            </group>
          )}
        </Suspense>

        {/* Chão com reflexões */}
        <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1, 0]} receiveShadow>
          <planeGeometry args={[50, 50]} />
          <meshStandardMaterial
            color="#1a1a1a"
            transparent
            opacity={0.3}
            metalness={0.9}
            roughness={0.1}
          />
        </mesh>
      </Canvas>

      {/* Controles desktop avançados */}
      <div className="absolute bottom-4 right-4 bg-black/70 text-white p-3 rounded-lg text-sm space-y-2">
        <div className="text-xs text-gray-300 mb-2">🖥️ CONTROLES DESKTOP</div>
        
        <button
          onClick={() => setUseOBJ(!useOBJ)}
          className="block w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors"
        >
          {useOBJ ? 'Usar Fallback' : 'Usar Modelo OBJ'}
        </button>

        {useOBJ && (
          <>
            <button
              onClick={() => setTextureMode(textureMode === 'shaded' ? 'pbr' : 'shaded')}
              className="block w-full px-4 py-2 bg-green-600 hover:bg-green-700 rounded transition-colors"
            >
              Textura: {textureMode === 'shaded' ? 'Shaded' : 'PBR'}
            </button>

            {textureMode === 'shaded' && (
              <button
                onClick={() => setTextureConfig((textureConfig + 1) % 4)}
                className="block w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded transition-colors"
              >
                Config: {textureConfig} ({['Original', 'Flip H', 'Flip V ✓', 'Flip HV'][textureConfig]})
              </button>
            )}
            
            <button
              onClick={() => setRealisticMaterial(!realisticMaterial)}
              className="block w-full px-4 py-2 bg-orange-600 hover:bg-orange-700 rounded transition-colors"
            >
              Material: {realisticMaterial ? 'Físico Premium ✨' : 'Standard'}
            </button>
          </>
        )}

        <button
          onClick={() => setRotation([0, 0, 0])}
          className="block w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded transition-colors text-xs"
        >
          Reset (R)
        </button>
      </div>

      {/* Status desktop detalhado */}
      <div className="absolute top-4 right-4 bg-black/70 text-white p-3 rounded-lg text-sm space-y-1">
        <div className="text-xs text-gray-300">🖥️ DESKTOP MODE</div>
        <div>Modelo: {useOBJ ? 'OBJ Premium' : 'Fallback'}</div>
        {useOBJ && (
          <>
            <div>Textura: {textureMode === 'shaded' ? 'Shaded Original' : 'PBR 4K'}</div>
            <div>Material: {realisticMaterial ? 'Físico Premium' : 'Standard'}</div>
            <div>Sombras: 8K Ultra</div>
            {textureMode === 'shaded' && (
              <div>Config: {textureConfig} (Flip V ✓)</div>
            )}
          </>
        )}
      </div>

      {/* Instruções desktop */}
      <div className="absolute bottom-4 left-4 bg-black/70 text-white p-3 rounded-lg text-xs space-y-1">
        <div className="text-gray-300">CONTROLES:</div>
        <div>🖱️ Arrastar: Rotacionar</div>
        <div>⌨️ Setas: Rotação precisa</div>
        <div>⌨️ R: Reset</div>
      </div>
    </div>
  )
}

export default Car3DDesktop
