-- Migration: Fix ride_requests table structure and relationships
-- This migration corrects issues with the ride requests functionality

-- Drop existing ride_requests table if it exists
DROP TABLE IF EXISTS ride_requests CASCADE;

-- Create ride_requests table with correct structure
CREATE TABLE ride_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    driver_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    
    -- Standardize location fields
    pickup_latitude DECIMAL(10, 8) NOT NULL,
    pickup_longitude DECIMAL(11, 8) NOT NULL,
    pickup_address TEXT NOT NULL,
    
    destination_latitude DECIMAL(10, 8) NOT NULL,
    destination_longitude DECIMAL(11, 8) NOT NULL,
    destination_address TEXT NOT NULL,
    
    -- Ride details
    vehicle_type VARCHAR(50) DEFAULT 'economy',
    estimated_distance DECIMAL(10, 2), -- in kilometers
    estimated_duration INTEGER, -- in seconds
    estimated_price DECIMAL(10, 2),
    final_price DECIMAL(10, 2),
    
    -- Payment info
    payment_method VARCHAR(50),
    payment_status VARCHAR(50) DEFAULT 'pending',
    
    -- Status tracking
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    
    -- Additional fields
    cancellation_reason TEXT,
    notes TEXT
);

-- Add necessary indexes
CREATE INDEX idx_ride_requests_user_id ON ride_requests(user_id);
CREATE INDEX idx_ride_requests_driver_id ON ride_requests(driver_id);
CREATE INDEX idx_ride_requests_status ON ride_requests(status);
CREATE INDEX idx_ride_requests_created_at ON ride_requests(created_at);
CREATE INDEX idx_ride_requests_pickup_coords ON ride_requests(pickup_latitude, pickup_longitude);

-- Add trigger for updating updated_at
CREATE TRIGGER update_ride_requests_updated_at
    BEFORE UPDATE ON ride_requests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE ride_requests ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own ride requests"
    ON ride_requests FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own ride requests"
    ON ride_requests FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own ride requests"
    ON ride_requests FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "Drivers can view assigned rides"
    ON ride_requests FOR SELECT
    USING (auth.uid() = driver_id);

CREATE POLICY "Drivers can update assigned rides"
    ON ride_requests FOR UPDATE
    USING (auth.uid() = driver_id);

-- Create function to request a ride
CREATE OR REPLACE FUNCTION request_ride(
    pickup_lat DECIMAL,
    pickup_lng DECIMAL,
    pickup_addr TEXT,
    dest_lat DECIMAL,
    dest_lng DECIMAL,
    dest_addr TEXT,
    vehicle_type_req VARCHAR DEFAULT 'economy',
    payment_method_req VARCHAR DEFAULT 'cash'
)
RETURNS UUID AS $$
DECLARE
    new_ride_id UUID;
BEGIN
    INSERT INTO ride_requests (
        user_id,
        pickup_latitude,
        pickup_longitude,
        pickup_address,
        destination_latitude,
        destination_longitude,
        destination_address,
        vehicle_type,
        payment_method,
        status
    ) VALUES (
        auth.uid(),
        pickup_lat,
        pickup_lng,
        pickup_addr,
        dest_lat,
        dest_lng,
        dest_addr,
        vehicle_type_req,
        payment_method_req,
        'pending'
    )
    RETURNING id INTO new_ride_id;
    
    RETURN new_ride_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to accept a ride
CREATE OR REPLACE FUNCTION accept_ride(ride_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE ride_requests
    SET 
        driver_id = auth.uid(),
        status = 'accepted',
        accepted_at = NOW()
    WHERE 
        id = ride_id 
        AND status = 'pending'
        AND driver_id IS NULL;
        
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to cancel a ride
CREATE OR REPLACE FUNCTION cancel_ride(
    ride_id UUID,
    reason TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE ride_requests
    SET 
        status = 'cancelled',
        cancelled_at = NOW(),
        cancellation_reason = reason
    WHERE 
        id = ride_id 
        AND status IN ('pending', 'accepted')
        AND (auth.uid() = user_id OR auth.uid() = driver_id);
        
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add necessary triggers
CREATE OR REPLACE FUNCTION validate_ride_request()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate coordinates
    IF NEW.pickup_latitude < -90 OR NEW.pickup_latitude > 90 OR
       NEW.destination_latitude < -90 OR NEW.destination_latitude > 90 OR
       NEW.pickup_longitude < -180 OR NEW.pickup_longitude > 180 OR
       NEW.destination_longitude < -180 OR NEW.destination_longitude > 180 THEN
        RAISE EXCEPTION 'Invalid coordinates';
    END IF;
    
    -- Validate status transitions
    IF TG_OP = 'UPDATE' THEN
        IF OLD.status = 'completed' AND NEW.status != 'completed' THEN
            RAISE EXCEPTION 'Cannot change status of completed ride';
        END IF;
        
        IF OLD.status = 'cancelled' AND NEW.status != 'cancelled' THEN
            RAISE EXCEPTION 'Cannot change status of cancelled ride';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER validate_ride_request_trigger
    BEFORE INSERT OR UPDATE ON ride_requests
    FOR EACH ROW
    EXECUTE FUNCTION validate_ride_request();