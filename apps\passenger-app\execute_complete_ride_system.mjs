// =====================================================
// EXECUTAR SCRIPT SQL COMPLETO NO SUPABASE
// =====================================================

import { createClient } from '@supabase/supabase-js'
import fs from 'fs'
import path from 'path'

// Configurações do Supabase - usando as mesmas do check_ride_fields.mjs
const supabaseUrl = 'https://udquhavmgqtpkubrfzdm.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI'

// Criar cliente Supabase
const supabase = createClient(supabaseUrl, supabaseKey)

// Função para verificar campos após a execução
async function checkRideFields() {
  console.log('\n🔍 VERIFICANDO CAMPOS DA TABELA')
  console.log('='.repeat(50))

  try {
    // Testar campos um por um
    const possibleFields = [
      'id', 'user_id', 'driver_id',
      'pickup_address', 'destination_address', 'origin_address',
      'pickup_lat', 'pickup_lng', 'pickup_latitude', 'pickup_longitude',
      'destination_lat', 'destination_lng', 'destination_latitude', 'destination_longitude',
      'origin_lat', 'origin_lng', 'origin_latitude', 'origin_longitude',
      'distance', 'duration', 'estimated_price', 'final_price',
      'status', 'vehicle_type', 'payment_method',
      'created_at', 'updated_at', 'accepted_at', 'started_at', 'completed_at', 'cancelled_at'
    ]

    const existingFields = []
    
    for (const field of possibleFields) {
      try {
        const { error } = await supabase
          .from('ride_requests')
          .select(field)
          .limit(1)
        
        if (!error) {
          existingFields.push(field)
          console.log(`✅ ${field}`)
        } else {
          console.log(`❌ ${field} - ${error.message}`)
        }
      } catch (e) {
        console.log(`❌ ${field} - ${e.message}`)
      }
    }

    console.log('\n📊 CAMPOS EXISTENTES:')
    console.log(existingFields.join(', '))

    // Tentar inserção com campos mínimos
    console.log('\n🧪 TESTANDO INSERÇÃO COM CAMPOS MÍNIMOS...')
    
    const minimalData = {
      user_id: '9ad5afad-8d2d-423e-b6e9-15e3e5a6ddc3'
    }
    
    // Adicionar campos que existem
    if (existingFields.includes('pickup_address')) {
      minimalData.pickup_address = 'Teste Origin'
    }
    if (existingFields.includes('status')) {
      minimalData.status = 'pending'
    }
    if (existingFields.includes('pickup_latitude')) {
      minimalData.pickup_latitude = 40.7128
    }
    if (existingFields.includes('pickup_longitude')) {
      minimalData.pickup_longitude = -74.0060
    }
    if (existingFields.includes('destination_latitude')) {
      minimalData.destination_latitude = 34.0522
    }
    if (existingFields.includes('destination_longitude')) {
      minimalData.destination_longitude = -118.2437
    }
    if (existingFields.includes('destination_address')) {
      minimalData.destination_address = 'Los Angeles, CA'
    }
    
    console.log('📝 Dados para inserção:', minimalData)
    
    const { data: insertData, error: insertError } = await supabase
      .from('ride_requests')
      .insert(minimalData)
      .select('id')
      .single()
    
    if (insertError) {
      console.error('❌ Erro na inserção mínima:', insertError)
    } else {
      console.log('✅ Inserção mínima bem-sucedida:', insertData)
      
      // Limpar
      await supabase
        .from('ride_requests')
        .delete()
        .eq('id', insertData.id)
      
      console.log('🧹 Registro removido')
    }

  } catch (error) {
    console.error('💥 Erro:', error)
  }
}

// Função para gerar instruções para o usuário
function generateInstructions() {
  console.log('\n📋 INSTRUÇÕES PARA EXECUTAR O SCRIPT SQL NO SUPABASE')
  console.log('='.repeat(50))
  console.log('\n1. Acesse o Dashboard do Supabase: https://app.supabase.com')
  console.log('2. Selecione seu projeto')
  console.log('3. No menu lateral, clique em "SQL Editor"')
  console.log('4. Clique em "+ New Query"')
  console.log('5. Copie e cole o conteúdo do arquivo complete_ride_system.sql')
  console.log('   Localizado em: database/migrations/complete_ride_system.sql')
  console.log('6. Clique em "Run" para executar o script')
  console.log('7. Após a execução, execute este script novamente para verificar se os campos foram criados corretamente')
  
  // Ler o arquivo SQL para mostrar o caminho
  const sqlFilePath = path.resolve('..', '..', 'database', 'migrations', 'complete_ride_system.sql')
  console.log(`\n📂 Caminho completo do arquivo SQL: ${sqlFilePath}`)
  
  if (fs.existsSync(sqlFilePath)) {
    console.log('✅ Arquivo SQL encontrado!')
  } else {
    console.log('❌ Arquivo SQL não encontrado. Verifique o caminho.')
  }
}

// Executar verificação e gerar instruções
async function main() {
  console.log('🚀 VERIFICAÇÃO DE CAMPOS E INSTRUÇÕES')
  console.log('='.repeat(50))
  
  // Verificar campos atuais
  console.log('\n📊 VERIFICANDO CAMPOS ATUAIS:')
  await checkRideFields()
  
  // Gerar instruções para o usuário
  generateInstructions()
}

main()
  .then(() => {
    console.log('\n🏁 PROCESSO CONCLUÍDO!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })