-- Verificar e corrigir problemas na tabela ride_requests

-- 1. Verificar se a tabela existe e criar se necessário
CREATE TABLE IF NOT EXISTS ride_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL,
    driver_id UUID,
    pickup_latitude DECIMAL(10, 8),
    pickup_longitude DECIMAL(11, 8),
    destination_latitude DECIMAL(10, 8),
    destination_longitude DECIMAL(11, 8),
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Adicionar constraints de chave estrangeira se não existirem
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'ride_requests_user_id_fkey'
    ) THEN
        ALTER TABLE ride_requests
        ADD CONSTRAINT ride_requests_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES auth.users(id);
    END IF;
END $$;

-- 3. Adicionar índices essenciais
CREATE INDEX IF NOT EXISTS idx_ride_requests_status ON ride_requests(status);
CREATE INDEX IF NOT EXISTS idx_ride_requests_coords ON ride_requests(pickup_latitude, pickup_longitude);

-- 4. Habilitar RLS
ALTER TABLE ride_requests ENABLE ROW LEVEL SECURITY;

-- 5. Adicionar políticas básicas de segurança
DROP POLICY IF EXISTS "Usuários podem ver suas próprias corridas" ON ride_requests;
CREATE POLICY "Usuários podem ver suas próprias corridas"
ON ride_requests FOR SELECT
USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Usuários podem criar corridas" ON ride_requests;
CREATE POLICY "Usuários podem criar corridas"
ON ride_requests FOR INSERT
WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Motoristas podem ver corridas atribuídas" ON ride_requests;
CREATE POLICY "Motoristas podem ver corridas atribuídas"
ON ride_requests FOR SELECT
USING (auth.uid() = driver_id);

-- 6. Função para validar coordenadas
CREATE OR REPLACE FUNCTION validate_coordinates()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.pickup_latitude IS NOT NULL AND (NEW.pickup_latitude < -90 OR NEW.pickup_latitude > 90) THEN
        RAISE EXCEPTION 'Latitude de origem inválida';
    END IF;
    IF NEW.pickup_longitude IS NOT NULL AND (NEW.pickup_longitude < -180 OR NEW.pickup_longitude > 180) THEN
        RAISE EXCEPTION 'Longitude de origem inválida';
    END IF;
    IF NEW.destination_latitude IS NOT NULL AND (NEW.destination_latitude < -90 OR NEW.destination_latitude > 90) THEN
        RAISE EXCEPTION 'Latitude de destino inválida';
    END IF;
    IF NEW.destination_longitude IS NOT NULL AND (NEW.destination_longitude < -180 OR NEW.destination_longitude > 180) THEN
        RAISE EXCEPTION 'Longitude de destino inválida';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7. Adicionar trigger de validação
DROP TRIGGER IF EXISTS validate_ride_coordinates ON ride_requests;
CREATE TRIGGER validate_ride_coordinates
    BEFORE INSERT OR UPDATE ON ride_requests
    FOR EACH ROW
    EXECUTE FUNCTION validate_coordinates();