-- Sistema completo de gerenciamento de corridas

-- 1. Enum para status das corridas
CREATE TYPE ride_status AS ENUM (
    'pending',      -- Solicitada pelo passageiro
    'searching',    -- Procurando motorista
    'accepted',     -- Motorista aceitou
    'arrived',      -- Motorista chegou ao local
    'in_progress',  -- Corrida em andamento
    'completed',    -- Finalizada com sucesso
    'cancelled'     -- Cancelada
);

-- 2. Enum para métodos de pagamento
CREATE TYPE payment_method AS ENUM (
    'cash',
    'credit_card',
    'pix'
);

-- 3. <PERSON><PERSON><PERSON> tabela ride_requests com todas as colunas necessárias
CREATE TABLE IF NOT EXISTS ride_requests (
    -- Identificação
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id),
    driver_id UUID REFERENCES auth.users(id),
    
    -- Localização
    pickup_latitude DECIMAL(10, 8) NOT NULL,
    pickup_longitude DECIMAL(11, 8) NOT NULL,
    pickup_address TEXT NOT NULL,
    destination_latitude DECIMAL(10, 8) NOT NULL,
    destination_longitude DECIMAL(11, 8) NOT NULL,
    destination_address TEXT NOT NULL,
    
    -- Detalhes da corrida
    vehicle_type VARCHAR(50) DEFAULT 'economy',
    estimated_distance DECIMAL(10, 2),
    estimated_duration INTEGER,
    estimated_price DECIMAL(10, 2),
    final_price DECIMAL(10, 2),
    
    -- Pagamento
    payment_method payment_method DEFAULT 'cash',
    payment_status VARCHAR(50) DEFAULT 'pending',
    
    -- Status e timestamps
    status ride_status DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    accepted_at TIMESTAMPTZ,
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    cancelled_at TIMESTAMPTZ,
    
    -- Informações adicionais
    cancellation_reason TEXT,
    notes TEXT,
    
    -- Metadados
    version INTEGER DEFAULT 1,
    is_test BOOLEAN DEFAULT FALSE
);

-- 4. Índices para otimização de performance
CREATE INDEX IF NOT EXISTS idx_ride_requests_user_id ON ride_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_ride_requests_driver_id ON ride_requests(driver_id);
CREATE INDEX IF NOT EXISTS idx_ride_requests_status ON ride_requests(status);
CREATE INDEX IF NOT EXISTS idx_ride_requests_created_at ON ride_requests(created_at);
CREATE INDEX IF NOT EXISTS idx_ride_requests_pickup_coords ON ride_requests(pickup_latitude, pickup_longitude);
CREATE INDEX IF NOT EXISTS idx_ride_requests_destination_coords ON ride_requests(destination_latitude, destination_longitude);

-- 5. Trigger para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    NEW.version = OLD.version + 1;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_ride_requests_timestamp
    BEFORE UPDATE ON ride_requests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 6. Validação de coordenadas e status
CREATE OR REPLACE FUNCTION validate_ride_request()
RETURNS TRIGGER AS $$
BEGIN
    -- Validar coordenadas
    IF NEW.pickup_latitude < -90 OR NEW.pickup_latitude > 90 OR
       NEW.destination_latitude < -90 OR NEW.destination_latitude > 90 OR
       NEW.pickup_longitude < -180 OR NEW.pickup_longitude > 180 OR
       NEW.destination_longitude < -180 OR NEW.destination_longitude > 180 THEN
        RAISE EXCEPTION 'Coordenadas inválidas';
    END IF;
    
    -- Validar transições de status
    IF TG_OP = 'UPDATE' THEN
        -- Não permitir alterações em corridas finalizadas
        IF OLD.status = 'completed' AND NEW.status != 'completed' THEN
            RAISE EXCEPTION 'Não é possível modificar uma corrida finalizada';
        END IF;
        
        -- Não permitir alterações em corridas canceladas
        IF OLD.status = 'cancelled' AND NEW.status != 'cancelled' THEN
            RAISE EXCEPTION 'Não é possível modificar uma corrida cancelada';
        END IF;
        
        -- Validar sequência de status
        IF OLD.status = 'pending' AND NEW.status NOT IN ('searching', 'cancelled') THEN
            RAISE EXCEPTION 'Status inválido para corrida pendente';
        END IF;
        
        IF OLD.status = 'searching' AND NEW.status NOT IN ('accepted', 'cancelled') THEN
            RAISE EXCEPTION 'Status inválido para corrida em busca';
        END IF;
        
        IF OLD.status = 'accepted' AND NEW.status NOT IN ('arrived', 'cancelled') THEN
            RAISE EXCEPTION 'Status inválido para corrida aceita';
        END IF;
        
        IF OLD.status = 'arrived' AND NEW.status NOT IN ('in_progress', 'cancelled') THEN
            RAISE EXCEPTION 'Status inválido para motorista chegou';
        END IF;
        
        IF OLD.status = 'in_progress' AND NEW.status NOT IN ('completed', 'cancelled') THEN
            RAISE EXCEPTION 'Status inválido para corrida em andamento';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER validate_ride_request_trigger
    BEFORE INSERT OR UPDATE ON ride_requests
    FOR EACH ROW
    EXECUTE FUNCTION validate_ride_request();

-- 7. Políticas de segurança (RLS)
ALTER TABLE ride_requests ENABLE ROW LEVEL SECURITY;

-- Política para passageiros
CREATE POLICY "Passageiros podem ver suas próprias corridas"
    ON ride_requests FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Passageiros podem criar corridas"
    ON ride_requests FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Passageiros podem atualizar suas corridas pendentes"
    ON ride_requests FOR UPDATE
    USING (auth.uid() = user_id AND status = 'pending');

-- Política para motoristas
CREATE POLICY "Motoristas podem ver corridas disponíveis"
    ON ride_requests FOR SELECT
    USING (status = 'searching');

CREATE POLICY "Motoristas podem ver suas corridas aceitas"
    ON ride_requests FOR SELECT
    USING (auth.uid() = driver_id);

CREATE POLICY "Motoristas podem atualizar suas corridas aceitas"
    ON ride_requests FOR UPDATE
    USING (auth.uid() = driver_id AND status IN ('accepted', 'arrived', 'in_progress'));

-- 8. Função para solicitar corrida
CREATE OR REPLACE FUNCTION request_ride(
    pickup_lat DECIMAL,
    pickup_lng DECIMAL,
    pickup_addr TEXT,
    dest_lat DECIMAL,
    dest_lng DECIMAL,
    dest_addr TEXT,
    vehicle_type_req VARCHAR DEFAULT 'economy',
    payment_method_req payment_method DEFAULT 'cash'
)
RETURNS UUID AS $$
DECLARE
    new_ride_id UUID;
BEGIN
    INSERT INTO ride_requests (
        user_id,
        pickup_latitude,
        pickup_longitude,
        pickup_address,
        destination_latitude,
        destination_longitude,
        destination_address,
        vehicle_type,
        payment_method,
        status
    ) VALUES (
        auth.uid(),
        pickup_lat,
        pickup_lng,
        pickup_addr,
        dest_lat,
        dest_lng,
        dest_addr,
        vehicle_type_req,
        payment_method_req,
        'pending'
    )
    RETURNING id INTO new_ride_id;
    
    -- Atualizar para searching imediatamente
    UPDATE ride_requests 
    SET status = 'searching'
    WHERE id = new_ride_id;
    
    RETURN new_ride_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Função para aceitar corrida
CREATE OR REPLACE FUNCTION accept_ride(ride_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE ride_requests
    SET 
        driver_id = auth.uid(),
        status = 'accepted',
        accepted_at = NOW()
    WHERE 
        id = ride_id 
        AND status = 'searching'
        AND driver_id IS NULL;
        
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. Função para cancelar corrida
CREATE OR REPLACE FUNCTION cancel_ride(
    ride_id UUID,
    reason TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE ride_requests
    SET 
        status = 'cancelled',
        cancelled_at = NOW(),
        cancellation_reason = reason
    WHERE 
        id = ride_id 
        AND status NOT IN ('completed', 'cancelled')
        AND (auth.uid() = user_id OR auth.uid() = driver_id);
        
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 11. Função para atualizar status da corrida
CREATE OR REPLACE FUNCTION update_ride_status(
    ride_id UUID,
    new_status ride_status
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE ride_requests
    SET status = new_status,
        started_at = CASE WHEN new_status = 'in_progress' THEN NOW() ELSE started_at END,
        completed_at = CASE WHEN new_status = 'completed' THEN NOW() ELSE completed_at END
    WHERE 
        id = ride_id 
        AND (auth.uid() = user_id OR auth.uid() = driver_id);
        
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 12. View para corridas ativas
CREATE OR REPLACE VIEW active_rides AS
SELECT 
    r.*,
    u.email as user_email,
    d.email as driver_email
FROM 
    ride_requests r
    LEFT JOIN auth.users u ON r.user_id = u.id
    LEFT JOIN auth.users d ON r.driver_id = d.id
WHERE 
    r.status NOT IN ('completed', 'cancelled');

-- 13. Índice para busca de corridas próximas
CREATE INDEX IF NOT EXISTS idx_ride_requests_active_location ON ride_requests (
    pickup_latitude,
    pickup_longitude
) WHERE status = 'searching';