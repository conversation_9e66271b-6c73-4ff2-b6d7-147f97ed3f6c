{"name": "mobidrive-passenger", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --port 3000", "build": "vite build", "build:prod": "vite build --mode production", "preview": "vite preview", "start": "vite --port 3000", "deploy": "npm run build:prod && echo 'Build concluído para deploy!'"}, "dependencies": {"@react-three/drei": "^9.96.1", "@react-three/fiber": "^8.15.19", "@supabase/supabase-js": "^2.50.0", "framer-motion": "^11.0.0", "lucide-react": "^0.344.0", "mapbox-gl": "^3.1.2", "pg": "^8.16.0", "puppeteer": "^24.10.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.1", "three": "^0.160.0", "ws": "^8.18.2"}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.1.0"}}