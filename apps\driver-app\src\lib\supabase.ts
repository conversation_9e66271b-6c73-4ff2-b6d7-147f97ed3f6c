import { supabase, TABLES } from '../../shared/src/services/supabase-client';
import { realtimeManager } from '../../shared/src/services/supabase-realtime-manager';
import { supabaseErrorHandler } from '../../shared/src/services/supabase-error-handler';
import { syncService } from '../../shared/src/services/supabase-sync-service';

// Configuração específica do app de motorista
const APP_CONFIG = {
  appType: 'driver',
  appVersion: '1.1.0',
  realtime: {
    autoconnect: true, // ✅ HABILITADO: Realtime ativo para notificações de corridas
  }
};

// Configurar headers específicos do app
if (supabase.realtime) {
  supabase.realtime.setAuth(supabase.auth.session()?.access_token || '');
}

// Exportar tudo necessário para o app
export {
  supabase,
  TABLES,
  realtimeManager,
  supabaseErrorHandler,
  syncService
};

// Registrar configuração do app
console.log(`🚗 MobiDrive Driver App v${APP_CONFIG.appVersion} - Supabase configurado`);

// Iniciar realtime ao carregar
if (typeof window !== 'undefined') {
  realtimeManager.connect().then(success => {
    if (success) {
      console.log('✅ Conexão realtime inicializada com sucesso');
    }
  });
}

export default supabase;
