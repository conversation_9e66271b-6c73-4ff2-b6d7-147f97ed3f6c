import { supabase, TABLES, supabaseHelpers } from '../../../shared/src/services/supabase-client';

// Configuração específica do app de motorista
const APP_CONFIG = {
  appType: 'driver',
  appVersion: '2.0.0',
  realtime: {
    autoconnect: false, // Conectar apenas quando necessário
  }
} as const;

// Exportar tudo necessário para o app
export {
  supabase,
  TABLES,
  supabaseHelpers
};

// Log de inicialização
if (process.env.NODE_ENV === 'development') {
  console.log(`🚗 MobiDrive Driver App v${APP_CONFIG.appVersion} - Supabase configurado`);
}

export default supabase;
