import { useEffect, useRef, useState, useCallback } from 'react';
import { RealtimeChannel } from '@supabase/supabase-js';
import { supabase } from '../services/supabase-client';

/**
 * Hook para facilitar o uso de conexões Realtime do Supabase
 * com gerenciamento automático do ciclo de vida
 * 
 * @param channelName Nome do canal
 * @param eventType Tipo do evento (ex: 'postgres_changes')
 * @param event Nome do evento (ex: 'INSERT')
 * @param options Opções adicionais
 */
export function useSupabaseRealtime<T = any>(
  channelName: string,
  eventType: string,
  event: string,
  options: {
    filter?: string;
    criticalChannel?: boolean;
    autoSubscribe?: boolean;
  } = {}
) {
  const { filter, criticalChannel = false, autoSubscribe = true } = options;
  
  // Estado para armazenar os dados recebidos
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  
  // Referência para o canal
  const channelRef = useRef<RealtimeChannel | null>(null);
  
  // Callback chamado quando novos dados são recebidos
  const handlePayload = useCallback((payload: any) => {
    try {
      setData(payload.new || payload);
      setIsLoading(false);
      setError(null);
    } catch (err) {
      console.error('❌ Erro ao processar dados do canal:', err);
      setError(err instanceof Error ? err : new Error('Erro desconhecido'));
    }
  }, []);
  
  // Inscrever-se no canal
  const subscribe = useCallback(() => {
    try {
      setIsLoading(true);

      // Criar canal usando o cliente Supabase diretamente
      const channel = supabase.channel(channelName);

      if (eventType === 'postgres_changes') {
        channel.on(
          'postgres_changes',
          {
            event: event as any,
            schema: 'public',
            filter: filter
          },
          handlePayload
        );
      } else if (eventType === 'broadcast') {
        channel.on('broadcast', { event }, handlePayload);
      }

      channel.subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          setIsLoading(false);
          console.log(`✅ Inscrito no canal: ${channelName}, evento: ${event}`);
        }
      });

      channelRef.current = channel;
    } catch (err) {
      console.error('❌ Erro ao inscrever-se no canal:', err);
      setError(err instanceof Error ? err : new Error('Erro na inscrição'));
      setIsLoading(false);
    }
  }, [channelName, eventType, event, filter, criticalChannel, handlePayload]);
  
  // Cancelar inscrição no canal
  const unsubscribe = useCallback(() => {
    if (channelRef.current) {
      try {
        supabase.removeChannel(channelRef.current);
        channelRef.current = null;
        console.log(`🔌 Cancelada inscrição no canal: ${channelName}`);
      } catch (err) {
        console.error('❌ Erro ao cancelar inscrição no canal:', err);
      }
    }
  }, [channelName]);
  
  // Reinscrever-se no canal
  const resubscribe = useCallback(() => {
    unsubscribe();
    subscribe();
  }, [unsubscribe, subscribe]);
  
  // Inscrever-se automaticamente ao montar o componente
  useEffect(() => {
    if (autoSubscribe) {
      subscribe();
    }
    
    // Limpar ao desmontar
    return () => {
      unsubscribe();
    };
  }, [subscribe, unsubscribe, autoSubscribe]);
  
  // Enviar dados pelo canal
  const send = useCallback(
    async (messageType: string, payload: any): Promise<boolean> => {
      if (!channelRef.current) {
        console.error('❌ Tentativa de enviar mensagem em canal não inscrito');
        return false;
      }
      
      try {
        await channelRef.current.send({
          type: messageType,
          payload
        });
        
        console.log(`✅ Mensagem enviada pelo canal: ${channelName}`);
        return true;
      } catch (err) {
        console.error('❌ Erro ao enviar mensagem pelo canal:', err);
        return false;
      }
    },
    [channelName]
  );
  
  return {
    data,
    isLoading,
    error,
    subscribe,
    unsubscribe,
    resubscribe,
    send,
    channel: channelRef.current
  };
}

/**
 * Hook para inscrever-se em alterações em tabelas do Postgres
 */
export function useSupabaseTable<T = any>(
  table: string,
  event: 'INSERT' | 'UPDATE' | 'DELETE' | '*' = '*',
  options: {
    filter?: string;
    criticalChannel?: boolean;
    autoSubscribe?: boolean;
  } = {}
) {
  return useSupabaseRealtime<T>(
    `table-${table}`,
    'postgres_changes',
    event,
    options
  );
}

/**
 * Hook para inscrever-se em canais de broadcast
 */
export function useSupabaseBroadcast<T = any>(
  channelName: string,
  event: string,
  options: {
    criticalChannel?: boolean;
    autoSubscribe?: boolean;
  } = {}
) {
  return useSupabaseRealtime<T>(
    `broadcast-${channelName}`,
    'broadcast',
    event,
    options
  );
}

/**
 * Hook para inscrever-se em atualizações de localização
 */
export function useLocationUpdates(
  entityId: string,
  entityType: 'driver' | 'passenger' = 'driver'
) {
  return useSupabaseRealtime<{
    latitude: number;
    longitude: number;
    heading?: number;
    speed?: number;
    timestamp: string;
  }>(
    `location-${entityType}-${entityId}`,
    'postgres_changes',
    'UPDATE',
    {
      filter: `${entityType}_id=eq.${entityId}`,
      criticalChannel: true
    }
  );
}

export default useSupabaseRealtime;