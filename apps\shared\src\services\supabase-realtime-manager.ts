/**
 * Sistema avançado para gerenciar conexões realtime do Supabase
 * Foco na sincronização contínua e confiabilidade para aplicação em produção
 */

import { supabase, supabaseHelpers } from './supabase-client';
import { RealtimeChannel } from '@supabase/supabase-js';

interface ChannelOptions {
  eventType?: string;
  event?: string;
  filter?: string;
  isActive?: boolean;
  criticalChannel?: boolean;
}

interface ChannelInfo {
  name: string;
  channel: RealtimeChannel;
  createdAt: number;
  lastActivity: number;
  options: ChannelOptions;
  callback?: Function;
  errorCount: number;
}

/**
 * Gerenciador de conexões Realtime do Supabase
 * Garante conexões robustas e reconexão automática em ambiente de produção
 */
class SupabaseRealtimeManager {
  private static instance: SupabaseRealtimeManager;
  private activeChannels: Map<string, ChannelInfo> = new Map();
  private healthCheckInterval: number | null = null;
  private maxErrorCount = 5;
  private isReconnecting = false;
  
  // Singleton pattern
  public static getInstance(): SupabaseRealtimeManager {
    if (!SupabaseRealtimeManager.instance) {
      SupabaseRealtimeManager.instance = new SupabaseRealtimeManager();
    }
    return SupabaseRealtimeManager.instance;
  }
  
  private constructor() {
    this.setupHealthCheck();
    this.setupConnectionMonitoring();
    
    // Força conexão no início
    if (typeof window !== 'undefined' && navigator.onLine) {
      this.connect();
    }
  }
  
  /**
   * Iniciar monitoramento de conexão
   */
  private setupConnectionMonitoring(): void {
    if (typeof window === 'undefined') return;
    
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));
    window.addEventListener('beforeunload', this.cleanup.bind(this));
    
    // Verificar conexão a cada 30 segundos
    this.healthCheckInterval = window.setInterval(() => {
      this.checkHealth();
    }, 30000);
  }
  
  /**
   * Conectar ao Realtime Supabase
   */
  public async connect(): Promise<boolean> {
    try {
      if (!supabase.realtime) {
        console.error('❌ Cliente Realtime do Supabase não disponível');
        return false;
      }
      
      console.log('🔄 Conectando ao Realtime do Supabase...');
      await supabase.realtime.connect();
      console.log('✅ Conexão Realtime estabelecida com sucesso');
      
      // Reconectar canais
      this.reconnectAllChannels();
      
      return true;
    } catch (error) {
      console.error('❌ Erro ao conectar ao Realtime do Supabase:', error);
      
      // Tentar novamente em 5 segundos
      setTimeout(() => {
        if (!this.isReconnecting) {
          this.connect();
        }
      }, 5000);
      
      return false;
    }
  }
  
  /**
   * Inscrever em um canal com tratamento de erros
   */
  public subscribe(
    channelName: string,
    eventType: string,
    event: string,
    callback: Function,
    options: ChannelOptions = {}
  ): RealtimeChannel {
    // Adicionar prefixo para evitar colisões
    const fullChannelName = `mobidrive-${channelName}`;
    
    // Cancelar inscrição existente se houver
    this.unsubscribe(fullChannelName);
    
    // Criar nova inscrição
    try {
      console.log(`📡 Inscrevendo em canal: ${fullChannelName}`);
      
      // Criar canal
      const channel = supabase.channel(fullChannelName);
      
      // Inscrever no evento
      channel.on(eventType, { event, ...options.filter ? { filter: options.filter } : {} }, (payload) => {
        try {
          // Atualizar último acesso
          const channelInfo = this.activeChannels.get(fullChannelName);
          if (channelInfo) {
            channelInfo.lastActivity = Date.now();
          }
          
          // Chamar callback
          callback(payload);
        } catch (error) {
          console.error(`❌ Erro no callback do canal ${fullChannelName}:`, error);
          
          // Incrementar contador de erros
          const channelInfo = this.activeChannels.get(fullChannelName);
          if (channelInfo) {
            channelInfo.errorCount++;
            
            // Recriar canal se muitos erros
            if (channelInfo.errorCount > this.maxErrorCount) {
              console.warn(`⚠️ Muitos erros no canal ${fullChannelName}, recriando...`);
              this.unsubscribe(fullChannelName);
              this.subscribe(channelName, eventType, event, callback, options);
            }
          }
        }
      });
      
      // Registrar listeners para status do canal
      channel.subscribe((status) => {
        console.log(`📡 Status do canal ${fullChannelName}: ${status}`);
        
        if (status === 'SUBSCRIBED') {
          console.log(`✅ Canal ${fullChannelName} conectado com sucesso`);
        } else if (status === 'CLOSED' || status === 'CHANNEL_ERROR') {
          console.warn(`⚠️ Canal ${fullChannelName} fechado ou com erro, tentando reconectar...`);
          
          // Tentar reconectar após um breve intervalo
          setTimeout(() => {
            if (this.activeChannels.has(fullChannelName)) {
              this.reconnectChannel(fullChannelName);
            }
          }, 2000);
        }
      });
      
      // Armazenar informações do canal
      this.activeChannels.set(fullChannelName, {
        name: fullChannelName,
        channel,
        createdAt: Date.now(),
        lastActivity: Date.now(),
        options: {
          eventType,
          event,
          ...options
        },
        callback,
        errorCount: 0
      });
      
      return channel;
    } catch (error) {
      console.error(`❌ Erro ao inscrever no canal ${fullChannelName}:`, error);
      
      // Tentar novamente após um breve intervalo
      setTimeout(() => {
        this.subscribe(channelName, eventType, event, callback, options);
      }, 3000);
      
      // Retornar canal dummy para evitar erros
      return supabase.channel('dummy-channel');
    }
  }
  
  /**
   * Cancelar inscrição em um canal
   */
  public unsubscribe(channelName: string): boolean {
    try {
      const channelInfo = this.activeChannels.get(channelName);
      
      if (channelInfo) {
        console.log(`🔌 Cancelando inscrição no canal: ${channelName}`);
        
        try {
          channelInfo.channel.unsubscribe();
        } catch (error) {
          console.warn(`⚠️ Erro ao cancelar inscrição no canal ${channelName}:`, error);
        }
        
        this.activeChannels.delete(channelName);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error(`❌ Erro ao cancelar inscrição no canal ${channelName}:`, error);
      this.activeChannels.delete(channelName);
      return false;
    }
  }
  
  /**
   * Reconectar um canal específico
   */
  private async reconnectChannel(channelName: string): Promise<boolean> {
    const channelInfo = this.activeChannels.get(channelName);
    
    if (!channelInfo) {
      return false;
    }
    
    try {
      console.log(`🔄 Reconectando canal: ${channelName}`);
      
      // Cancelar inscrição atual
      try {
        channelInfo.channel.unsubscribe();
      } catch (error) {
        console.warn(`⚠️ Erro ao cancelar inscrição durante reconexão do canal ${channelName}:`, error);
      }
      
      // Criar nova inscrição
      const { options, callback } = channelInfo;
      
      if (options.eventType && options.event && callback) {
        this.subscribe(
          channelName.replace('mobidrive-', ''), 
          options.eventType, 
          options.event, 
          callback, 
          options
        );
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error(`❌ Erro ao reconectar canal ${channelName}:`, error);
      
      // Tentar novamente após um breve intervalo
      setTimeout(() => {
        this.reconnectChannel(channelName);
      }, 5000);
      
      return false;
    }
  }
  
  /**
   * Reconectar todos os canais
   */
  private async reconnectAllChannels(): Promise<void> {
    console.log('🔄 Reconectando todos os canais...');
    
    this.isReconnecting = true;
    
    // Copiar canais ativos para evitar modificação durante iteração
    const activeChannels = Array.from(this.activeChannels.keys());
    
    for (const channelName of activeChannels) {
      await this.reconnectChannel(channelName);
      
      // Pequena pausa entre reconexões para não sobrecarregar
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    this.isReconnecting = false;
    
    console.log('✅ Reconexão de canais concluída');
  }
  
  /**
   * Verificar saúde das conexões
   */
  private async checkHealth(): Promise<void> {
    if (!navigator.onLine) {
      return;
    }
    
    try {
      // Verificar conexão com Supabase
      const { connected } = await supabaseHelpers.checkConnection();
      
      if (!connected) {
        console.warn('⚠️ Conexão com Supabase perdida, tentando reconectar...');
        this.connect();
        return;
      }
      
      // Verificar se o Realtime está conectado
      if (supabase.realtime && !supabase.realtime.isConnected()) {
        console.warn('⚠️ Realtime desconectado, reconectando...');
        await this.connect();
      }
      
      // Verificar canais inativos
      const now = Date.now();
      const inactiveThreshold = 10 * 60 * 1000; // 10 minutos
      
      for (const [channelName, channelInfo] of this.activeChannels.entries()) {
        // Reconectar canais críticos independentemente do tempo
        if (channelInfo.options.criticalChannel) {
          this.reconnectChannel(channelName);
          continue;
        }
        
        // Verificar canais inativos
        if (now - channelInfo.lastActivity > inactiveThreshold) {
          console.log(`⏰ Canal ${channelName} inativo por mais de 10 minutos, reconectando...`);
          this.reconnectChannel(channelName);
        }
      }
    } catch (error) {
      console.error('❌ Erro ao verificar saúde das conexões:', error);
    }
  }
  
  /**
   * Lidar com evento online
   */
  private handleOnline(): void {
    console.log('🌐 Conexão restaurada, reconectando Supabase...');
    this.connect();
  }
  
  /**
   * Lidar com evento offline
   */
  private handleOffline(): void {
    console.log('🔌 Conexão perdida, pausando Supabase...');
    // Não desconecta completamente para facilitar reconexão
  }
  
  /**
   * Limpar recursos ao desmontar
   */
  public cleanup(): void {
    console.log('🧹 Limpando recursos do Realtime Manager...');
    
    // Cancelar verificação de saúde
    if (this.healthCheckInterval !== null) {
      clearInterval(this.healthCheckInterval);
    }
    
    // Cancelar inscrições
    for (const channelName of this.activeChannels.keys()) {
      this.unsubscribe(channelName);
    }
    
    // Limpar event listeners
    if (typeof window !== 'undefined') {
      window.removeEventListener('online', this.handleOnline);
      window.removeEventListener('offline', this.handleOffline);
      window.removeEventListener('beforeunload', this.cleanup);
    }
  }
  
  /**
   * Obter estatísticas
   */
  public getStats(): { totalChannels: number, channelNames: string[] } {
    return {
      totalChannels: this.activeChannels.size,
      channelNames: Array.from(this.activeChannels.keys())
    };
  }
}

// Exportar instância única para uso em toda a aplicação
export const realtimeManager = SupabaseRealtimeManager.getInstance();

// Exportar também como default
export default realtimeManager;