import React, { useRef, useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import mapboxgl from 'mapbox-gl'
import { MapPin, Navigation, Zap, Clock } from 'lucide-react'
import 'mapbox-gl/dist/mapbox-gl.css'

// Set Mapbox access token from environment
const MAPBOX_TOKEN = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN
if (!MAPBOX_TOKEN) {
  console.error('❌ VITE_MAPBOX_ACCESS_TOKEN não configurado!')
}
mapboxgl.accessToken = MAPBOX_TOKEN

interface MapComponentProps {
  onLocationSelect?: (coordinates: [number, number]) => void
  showDrivers?: boolean
  interactive?: boolean
  height?: string
  className?: string
}

interface Driver {
  id: string
  name: string
  coordinates: [number, number]
  rating: number
  eta: number
  vehicleType: 'car' | 'motorcycle'
}

export const MapComponent: React.FC<MapComponentProps> = ({
  onLocationSelect,
  showDrivers = false, // DESABILITADO: Motoristas próximos desativados
  interactive = true,
  height = "400px",
  className = ""
}) => {
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<mapboxgl.Map | null>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null)
  const [nearbyDrivers, setNearbyDrivers] = useState<Driver[]>([])
  const [selectedDriver, setSelectedDriver] = useState<Driver | null>(null)

  useEffect(() => {
    if (!mapContainer.current) return

    // Get user location
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const coords: [number, number] = [
          position.coords.longitude,
          position.coords.latitude
        ]
        setUserLocation(coords)
        initializeMap(coords)
      },
      () => {
        // Fallback to São Paulo
        const defaultCoords: [number, number] = [-46.6333, -23.5505]
        setUserLocation(defaultCoords)
        initializeMap(defaultCoords)
      }
    )

    return () => {
      if (map.current) {
        map.current.remove()
      }
    }
  }, [])

  const initializeMap = (coords: [number, number]) => {
    if (map.current) return

    map.current = new mapboxgl.Map({
      container: mapContainer.current!,
      style: 'mapbox://styles/mapbox/dark-v11', // 🌙 TEMA PRETO: Aplicado também no MapComponent
      center: coords,
      zoom: 14,
      interactive,
      attributionControl: false,
    })

    map.current.on('load', () => {
      setIsLoaded(true)

      // Add user location marker
      const userMarker = new mapboxgl.Marker({
        color: '#3b82f6',
        scale: 1.2
      })
        .setLngLat(coords)
        .addTo(map.current!)

      // Add click handler for location selection
      if (onLocationSelect && interactive) {
        map.current!.on('click', (e) => {
          onLocationSelect([e.lngLat.lng, e.lngLat.lat])
        })
      }

      // DESABILITADO: Geração de motoristas próximos desativada
      // Generate mock nearby drivers
      if (false) { // showDrivers desabilitado permanentemente
        generateNearbyDrivers(coords)
      }
    })

    // Add navigation controls
    if (interactive) {
      map.current.addControl(new mapboxgl.NavigationControl(), 'top-right')
    }
  }

  const generateNearbyDrivers = (userCoords: [number, number]) => {
    const drivers: Driver[] = [
      {
        id: '1',
        name: 'João Silva',
        coordinates: [userCoords[0] + 0.01, userCoords[1] + 0.005],
        rating: 4.8,
        eta: 3,
        vehicleType: 'car'
      },
      {
        id: '2',
        name: 'Maria Santos',
        coordinates: [userCoords[0] - 0.008, userCoords[1] - 0.003],
        rating: 4.9,
        eta: 5,
        vehicleType: 'motorcycle'
      },
      {
        id: '3',
        name: 'Pedro Costa',
        coordinates: [userCoords[0] + 0.005, userCoords[1] - 0.008],
        rating: 4.7,
        eta: 7,
        vehicleType: 'car'
      },
    ]

    setNearbyDrivers(drivers)

    // Add driver markers
    drivers.forEach((driver) => {
      const driverElement = document.createElement('div')
      driverElement.className = 'driver-marker'
      driverElement.innerHTML = `
        <div class="w-10 h-10 bg-green-500 rounded-full border-2 border-white shadow-lg flex items-center justify-center cursor-pointer hover:scale-110 transition-transform">
          <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/>
            <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z"/>
          </svg>
        </div>
      `

      driverElement.addEventListener('click', () => {
        setSelectedDriver(driver)
      })

      new mapboxgl.Marker(driverElement)
        .setLngLat(driver.coordinates)
        .addTo(map.current!)
    })
  }

  return (
    <div className={`relative ${className}`} style={{ height }}>
      {/* Map Container */}
      <div
        ref={mapContainer}
        className="w-full h-full rounded-2xl overflow-hidden shadow-large"
      />

      {/* Loading Overlay */}
      {!isLoaded && (
        <div className="absolute inset-0 bg-gray-100 rounded-2xl flex items-center justify-center">
          <div className="text-center">
            <motion.div
              className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            <p className="text-gray-500 text-sm">Carregando mapa...</p>
          </div>
        </div>
      )}

      {/* Driver Info Panel */}
      {selectedDriver && (
        <motion.div
          className="absolute bottom-4 left-4 right-4 glass-card p-4"
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white font-semibold">
                  {selectedDriver.name.charAt(0)}
                </span>
              </div>
              <div>
                <h3 className="font-semibold text-white">{selectedDriver.name}</h3>
                <div className="flex items-center space-x-2 text-sm text-white/80">
                  <div className="flex items-center">
                    <span className="text-yellow-400">★</span>
                    <span className="ml-1">{selectedDriver.rating}</span>
                  </div>
                  <span>•</span>
                  <div className="flex items-center">
                    <Clock className="w-3 h-3 mr-1" />
                    <span>{selectedDriver.eta} min</span>
                  </div>
                  <span>•</span>
                  <span className="capitalize">{selectedDriver.vehicleType}</span>
                </div>
              </div>
            </div>
            <motion.button
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => {
                // Handle driver selection
                console.log('Driver selected:', selectedDriver)
              }}
            >
              Escolher
            </motion.button>
          </div>
        </motion.div>
      )}

      {/* Map Controls */}
      {interactive && (
        <div className="absolute top-4 left-4 space-y-2">
          <motion.button
            className="glass-card p-3 text-white hover:bg-white/20 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => {
              if (userLocation && map.current) {
                map.current.flyTo({
                  center: userLocation,
                  zoom: 14,
                  duration: 1000
                })
              }
            }}
          >
            <Navigation className="w-5 h-5" />
          </motion.button>

          <motion.button
            className="glass-card p-3 text-white hover:bg-white/20 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => {
              // DESABILITADO: Refresh de motoristas próximos desativado
              // Refresh nearby drivers
              if (false && userLocation) { // Funcionalidade desabilitada
                generateNearbyDrivers(userLocation)
              }
            }}
          >
            <Zap className="w-5 h-5" />
          </motion.button>
        </div>
      )}

      {/* DESABILITADO: Badge de contagem de motoristas desativado */}
      {/* Driver Count Badge */}
      {false && showDrivers && nearbyDrivers.length > 0 && (
        <motion.div
          className="absolute top-4 right-4 glass-card px-3 py-2"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.5 }}
        >
          <div className="flex items-center space-x-2 text-white text-sm">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>{nearbyDrivers.length} motoristas próximos</span>
          </div>
        </motion.div>
      )}
    </div>
  )
}

export default MapComponent
