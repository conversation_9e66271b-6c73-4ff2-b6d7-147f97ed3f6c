// apps/shared/lib/types.ts

export type PaymentMethodType = 'cash' | 'credit_card' | 'debit_card' | 'pix' | 'digital_wallet' | 'voucher' | 'other';

export interface PaymentMethodDisplayInfo {
  icon?: string;
  display_name?: string;
  description?: string;
}

export interface PaymentMethod {
  id: string;
  user_id: string;
  type: PaymentMethodType;
  name: string;
  is_default: boolean;
  is_active: boolean;
  created_at?: string; // ISO date string
  updated_at?: string; // ISO date string

  // Card specific
  card_last_four?: string | null;
  card_brand?: string | null;
  card_holder_name?: string | null;
  card_expiry_month?: string | null;
  card_expiry_year?: string | null;

  // PIX specific
  pix_key?: string | null;
  pix_key_type?: string | null; // e.g., 'cpf', 'email', 'phone', 'random'

  // Cash specific
  cash_enabled?: boolean | null;
  cash_change_limit?: number | null;
  cash_notes?: string | null;

  // Voucher specific
  voucher_code?: string | null;
  voucher_balance?: number | null;
  voucher_expiry_date?: string | null;

  // Nickname / custom label
  nickname?: string | null;

  // Display info (potentially from a related table or constructed)
  display_info?: PaymentMethodDisplayInfo | null;

  // Outros campos que possam existir na view payment_methods_detailed
  [key: string]: any; // Allow other properties if coming from a view
}
