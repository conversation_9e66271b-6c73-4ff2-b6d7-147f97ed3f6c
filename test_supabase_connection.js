// Teste de conexão com Supabase
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://udquhavmgqtpkubrfzdm.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testConnection() {
  console.log('🔄 Testando conexão com Supabase...')
  console.log('URL:', supabaseUrl)
  console.log('Chave Anon:', supabaseAnonKey.substring(0, 20) + '...')
  
  try {
    // Teste 1: Verificar se consegue conectar
    const { data, error } = await supabase.from('profiles').select('count').limit(1)
    
    if (error) {
      console.error('❌ Erro na conexão:', error.message)
      console.error('Detalhes:', error)
    } else {
      console.log('✅ Conexão com Supabase estabelecida com sucesso!')
      console.log('Dados retornados:', data)
    }
    
    // Teste 2: Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError) {
      console.log('ℹ️ Usuário não autenticado (normal para teste):', authError.message)
    } else {
      console.log('👤 Usuário autenticado:', user)
    }
    
    // Teste 3: Listar tabelas disponíveis (tentativa)
    console.log('\n🔍 Testando acesso às tabelas...')
    
    const tables = ['profiles', 'drivers', 'rides', 'ride_requests', 'driver_locations']
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase.from(table).select('*').limit(1)
        if (error) {
          console.log(`❌ Tabela '${table}': ${error.message}`)
        } else {
          console.log(`✅ Tabela '${table}': Acessível (${data?.length || 0} registros encontrados)`)
        }
      } catch (err) {
        console.log(`❌ Tabela '${table}': Erro inesperado - ${err.message}`)
      }
    }
    
  } catch (error) {
    console.error('❌ Erro geral:', error)
  }
}

testConnection()