import React, { useEffect, useState } from 'react';
import { healthMonitor, HealthStatus } from '../services/supabase-health-monitor';

// Estilos para os diferentes status
const statusStyles = {
  [HealthStatus.HEALTHY]: {
    bg: 'bg-green-100',
    border: 'border-green-400',
    text: 'text-green-800',
    icon: '✅',
    title: 'Conexão estável'
  },
  [HealthStatus.DEGRADED]: {
    bg: 'bg-yellow-100',
    border: 'border-yellow-400',
    text: 'text-yellow-800',
    icon: '⚠️',
    title: 'Conexão instável'
  },
  [HealthStatus.UNHEALTHY]: {
    bg: 'bg-red-100',
    border: 'border-red-400',
    text: 'text-red-800',
    icon: '❌',
    title: 'Problema de conexão'
  },
  [HealthStatus.OFFLINE]: {
    bg: 'bg-gray-100',
    border: 'border-gray-400',
    text: 'text-gray-800',
    icon: '🔌',
    title: 'Offline'
  },
  [HealthStatus.UNKNOWN]: {
    bg: 'bg-blue-100',
    border: 'border-blue-400',
    text: 'text-blue-800',
    icon: '❓',
    title: 'Verificando conexão'
  }
};

interface SupabaseConnectionStatusProps {
  showDetails?: boolean;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'custom';
  className?: string;
  style?: React.CSSProperties;
  compact?: boolean;
  onStatusChange?: (status: HealthStatus) => void;
}

/**
 * Componente para exibir o status da conexão com o Supabase
 */
export const SupabaseConnectionStatus: React.FC<SupabaseConnectionStatusProps> = ({
  showDetails = false,
  position = 'bottom-right',
  className = '',
  style = {},
  compact = false,
  onStatusChange
}) => {
  const [healthCheck, setHealthCheck] = useState(healthMonitor.getLastCheck());
  const [expanded, setExpanded] = useState(false);
  
  // Registrar listener para atualizações de saúde
  useEffect(() => {
    const listenerId = healthMonitor.addListener((check) => {
      setHealthCheck(check);
      
      // Notificar sobre mudança de status
      if (onStatusChange && check.status !== healthCheck?.status) {
        onStatusChange(check.status);
      }
    });
    
    // Verificar saúde imediatamente
    healthMonitor.checkHealth();
    
    return () => {
      healthMonitor.removeListener(listenerId);
    };
  }, [healthCheck?.status, onStatusChange]);
  
  // Se não houver verificação, não exibir nada
  if (!healthCheck) {
    return null;
  }
  
  // Obter estilos para o status atual
  const status = healthCheck.status || HealthStatus.UNKNOWN;
  const style = statusStyles[status];
  
  // Determinar posicionamento
  let positionClasses = '';
  switch (position) {
    case 'top-right':
      positionClasses = 'fixed top-4 right-4';
      break;
    case 'top-left':
      positionClasses = 'fixed top-4 left-4';
      break;
    case 'bottom-right':
      positionClasses = 'fixed bottom-4 right-4';
      break;
    case 'bottom-left':
      positionClasses = 'fixed bottom-4 left-4';
      break;
    default:
      positionClasses = '';
  }
  
  // Renderizar versão compacta (apenas ícone)
  if (compact) {
    return (
      <div 
        className={`rounded-full w-8 h-8 flex items-center justify-center cursor-pointer shadow-sm ${style.bg} ${style.border} ${positionClasses} ${className}`}
        onClick={() => setExpanded(!expanded)}
        title={`${style.title}: ${healthCheck.message}`}
        style={style}
      >
        <span role="img" aria-label={style.title}>
          {style.icon}
        </span>
        
        {/* Exibir popover com detalhes quando expandido */}
        {expanded && (
          <div className="absolute bottom-full right-0 mb-2 w-64 rounded-md shadow-lg bg-white p-3 text-sm">
            <div className="font-semibold mb-1">{style.title}</div>
            <div className="text-gray-600 mb-2">{healthCheck.message}</div>
            <div className="text-xs text-gray-500">
              Latência: {healthCheck.latency}ms
              <br />
              Atualizado: {new Date(healthCheck.timestamp).toLocaleTimeString()}
            </div>
            {showDetails && healthCheck.details && (
              <div className="mt-2 text-xs text-gray-600">
                <div className="font-medium">Detalhes:</div>
                <ul className="list-disc list-inside">
                  <li>
                    Realtime: {healthCheck.details.realtimeConnected ? 'Conectado' : 'Desconectado'}
                  </li>
                  <li>
                    Autenticação: {healthCheck.details.authSession ? 'Ativa' : 'Inativa'}
                  </li>
                </ul>
              </div>
            )}
          </div>
        )}
      </div>
    );
  }
  
  // Renderizar versão completa
  return (
    <div 
      className={`rounded-md p-3 ${style.bg} ${style.text} ${style.border} border shadow-sm ${positionClasses} ${className}`}
      style={style}
    >
      <div className="flex items-center">
        <span role="img" aria-label={style.title} className="mr-2">
          {style.icon}
        </span>
        <div>
          <div className="font-medium">{style.title}</div>
          <div className="text-sm">{healthCheck.message}</div>
        </div>
      </div>
      
      {showDetails && (
        <div className="mt-2 text-xs border-t border-opacity-20 pt-2">
          <div>Latência: {healthCheck.latency}ms</div>
          <div>Atualizado: {new Date(healthCheck.timestamp).toLocaleTimeString()}</div>
          
          {healthCheck.details && (
            <div className="mt-1">
              <div className="font-medium">Detalhes:</div>
              <ul className="list-disc list-inside">
                <li>
                  Realtime: {healthCheck.details.realtimeConnected ? 'Conectado' : 'Desconectado'}
                </li>
                <li>
                  Autenticação: {healthCheck.details.authSession ? 'Ativa' : 'Inativa'}
                </li>
              </ul>
            </div>
          )}
          
          <button 
            className={`mt-2 px-2 py-1 text-xs rounded ${style.bg} ${style.text} border ${style.border} hover:opacity-80`}
            onClick={() => healthMonitor.checkHealth()}
          >
            Verificar novamente
          </button>
        </div>
      )}
    </div>
  );
};

export default SupabaseConnectionStatus;