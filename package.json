{"name": "mobdrive-main", "version": "2.0.0", "description": "Sistema completo de gestão para serviços de mobilidade urbana, incluindo aplicações para passageiros, motoristas e administradores.", "type": "module", "scripts": {"test": "node scripts/test-supabase-connection.cjs", "test:connection": "node scripts/test-supabase-connection.cjs", "monitor": "node scripts/supabase-monitor.cjs", "db:install": "psql -f database/install.sql", "dev": "echo \"Use npm run dev nos apps específicos (passenger-app, driver-app, admin-app)\"", "build": "echo \"Use npm run build nos apps específicos\"", "lint": "echo \"Linting não configurado ainda\"", "clean": "rm -rf node_modules && rm -rf apps/*/node_modules"}, "keywords": ["mobility", "transport", "ride-sharing", "supabase", "react", "typescript"], "author": "MobDrive Team", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.50.0", "dotenv": "^16.5.0"}, "devDependencies": {"cors": "^2.8.5", "express": "^4.21.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}