# Instalação do Sistema MobiDrive

Este diretório contém os scripts necessários para configurar o banco de dados do sistema MobiDrive.

## Pré-requisitos

1. PostgreSQL instalado e configurado
2. Acesso ao banco de dados Supabase
3. Permissões de administrador no banco de dados

## Arquivos do Sistema

- `install.sql` - Script principal de instalação
- `install.bat` - Script de automação para Windows
- `complete_ride_system.sql` - Sistema base de corridas
- `add_driver_location_tracking.sql` - Sistema de rastreamento
- `fix_ride_requests_essential.sql` - Correções e otimizações
- `test_ride_system.sql` - Testes automatizados

## Instalação

### Método Automatizado (Windows)

1. Abra o arquivo `install.bat` em um editor de texto
2. Configure as variáveis de ambiente com suas credenciais do Supabase:
   ```batch
   set PGHOST=db.SEUHOSTSUPABASE.supabase.co
   set PGDATABASE=postgres
   set PGUSER=postgres
   set PGPASSWORD=SUASENHA
   ```
3. Salve o arquivo
4. Execute `install.bat`

### Instalação Manual

Se preferir instalar manualmente, execute os scripts na seguinte ordem:

1. `complete_ride_system.sql`
2. `add_driver_location_tracking.sql`
3. `fix_ride_requests_essential.sql`
4. `test_ride_system.sql`

## Verificação da Instalação

Após a instalação, o sistema criará:

1. Tabelas principais:
   - `ride_requests` - Solicitações de corrida
   - `driver_locations` - Localização dos motoristas
   - `driver_profiles` - Perfis dos motoristas

2. Funções essenciais:
   - `request_ride()` - Solicitar uma corrida
   - `update_driver_location()` - Atualizar localização do motorista
   - `get_nearby_drivers()` - Encontrar motoristas próximos

3. Políticas de segurança (RLS)
4. Índices de performance
5. Triggers e validações

## Teste do Sistema

O script `test_ride_system.sql` realiza testes automatizados verificando:

1. Criação de corridas
2. Atualização de status
3. Validações de dados
4. Políticas de segurança
5. Performance das consultas

## Solução de Problemas

Se encontrar algum erro durante a instalação:

1. Verifique os logs de instalação
2. Confirme as credenciais do banco de dados
3. Verifique as permissões do usuário
4. Execute os scripts manualmente para identificar o problema específico

## Estrutura do Banco de Dados

### Tabela ride_requests
- Gerencia todas as solicitações de corrida
- Controla status e pagamentos
- Armazena localizações e preços

### Tabela driver_locations
- Rastreia localização dos motoristas
- Atualiza em tempo real
- Otimizada para consultas geográficas

### Tabela driver_profiles
- Armazena informações dos motoristas
- Gerencia veículos e documentação
- Controla avaliações e status

## Segurança

O sistema implementa:

1. Row Level Security (RLS)
2. Validação de dados
3. Controle de acesso por função
4. Proteção contra injeção SQL
5. Logging de alterações

## Manutenção

Recomendamos:

1. Backup diário do banco de dados
2. Monitoramento de performance
3. Limpeza periódica de dados antigos
4. Verificação regular dos logs

## Suporte

Em caso de problemas:

1. Verifique os logs do PostgreSQL
2. Consulte a documentação do Supabase
3. Execute os testes automatizados
4. Verifique as políticas de segurança

## Atualizações

Ao atualizar o sistema:

1. Faça backup do banco de dados
2. Execute os scripts de migração
3. Verifique os logs de erro
4. Teste todas as funcionalidades

## Otimizações

O sistema inclui:

1. Índices otimizados
2. Triggers eficientes
3. Consultas otimizadas
4. Gestão de cache
5. Monitoramento de performance