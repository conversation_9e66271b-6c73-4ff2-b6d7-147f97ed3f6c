import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { supabase } from './lib/supabase'

// ⚡ CONFIGURAÇÃO OTIMIZADA - COMUNICAÇÃO SUPABASE PARA DRIVER APP
// A comunicação robusta é essencial para o app do motorista que envia atualizações
// de localização e precisa receber notificações de corridas em tempo real

// Configurar funcionalidades de reconexão e monitoramento
const setupDriverAppSupabaseConnection = () => {
  // Verificar conexão inicial
  const checkInitialConnection = async () => {
    try {
      const { data, error } = await supabase.from('ride_requests').select('count').limit(1)
      console.log(`✅ Conexão inicial com Supabase: ${error ? 'falha' : 'sucesso'}`)
      
      // Verificar conexão realtime
      if (supabase.realtime && !supabase.realtime.isConnected()) {
        await supabase.realtime.connect()
        console.log('✅ Conexão Realtime estabelecida')
      }
    } catch (e) {
      console.warn('⚠️ Erro na verificação inicial:', e)
      // Tentar novamente após 3 segundos
      setTimeout(checkInitialConnection, 3000)
    }
  }

  // Cache de operações pendentes para sincronização
  const pendingOperations: Array<{
    table: string;
    operation: 'insert' | 'update' | 'upsert';
    data: any;
    timestamp: number;
  }> = []

  // Função para adicionar operação ao cache
  const addPendingOperation = (
    table: string,
    operation: 'insert' | 'update' | 'upsert',
    data: any
  ) => {
    pendingOperations.push({
      table,
      operation,
      data,
      timestamp: Date.now()
    })
    
    // Salvar no localStorage
    try {
      localStorage.setItem('pending_operations', JSON.stringify(pendingOperations))
    } catch (e) {
      console.error('❌ Erro ao salvar operações pendentes:', e)
    }
    
    // Tentar sincronizar imediatamente se estiver online
    if (navigator.onLine) {
      syncPendingOperations()
    }
  }

  // Função para sincronizar operações pendentes
  const syncPendingOperations = async () => {
    if (pendingOperations.length === 0) return
    
    // Copiar lista atual para evitar modificações durante o processamento
    const operations = [...pendingOperations]
    
    for (let i = 0; i < operations.length; i++) {
      const op = operations[i]
      
      try {
        let result
        
        // Executar operação
        switch (op.operation) {
          case 'insert':
            result = await supabase.from(op.table).insert(op.data)
            break
          case 'update':
            result = await supabase.from(op.table).update(op.data)
              .eq(op.data.id ? 'id' : 'user_id', op.data.id || op.data.user_id)
            break
          case 'upsert':
            result = await supabase.from(op.table).upsert(op.data)
            break
        }
        
        // Se operação bem-sucedida, remover da lista
        if (!result.error) {
          // Remover operação da lista original
          const index = pendingOperations.findIndex(
            p => p.timestamp === op.timestamp && p.table === op.table
          )
          if (index >= 0) {
            pendingOperations.splice(index, 1)
          }
          
          console.log(`✅ Operação sincronizada com sucesso: ${op.operation} em ${op.table}`)
        } else {
          console.warn(`⚠️ Erro ao sincronizar operação:`, result.error)
        }
      } catch (e) {
        console.error(`❌ Erro ao processar operação ${op.operation} em ${op.table}:`, e)
      }
    }
    
    // Atualizar localStorage
    try {
      localStorage.setItem('pending_operations', JSON.stringify(pendingOperations))
    } catch (e) {
      console.error('❌ Erro ao atualizar operações pendentes:', e)
    }
  }

  // Carregar operações pendentes do localStorage
  try {
    const saved = localStorage.getItem('pending_operations')
    if (saved) {
      const parsed = JSON.parse(saved)
      if (Array.isArray(parsed)) {
        pendingOperations.push(...parsed)
        console.log(`📋 Carregadas ${pendingOperations.length} operações pendentes`)
      }
    }
  } catch (e) {
    console.error('❌ Erro ao carregar operações pendentes:', e)
  }

  // Configurar reconexão automática
  if (typeof window !== 'undefined') {
    // Monitorar eventos de conexão
    window.addEventListener('online', async () => {
      console.log('🌐 Conexão de internet restaurada. Reconectando Supabase...')
      try {
        if (supabase.realtime && !supabase.realtime.isConnected()) {
          await supabase.realtime.connect()
          console.log('✅ Conexão Realtime restabelecida')
        }
        
        // Sincronizar operações pendentes
        syncPendingOperations()
      } catch (e) {
        console.error('❌ Erro ao reconectar Realtime:', e)
      }
    })

    window.addEventListener('offline', () => {
      console.log('🔌 Conexão de internet perdida. Armazenando operações localmente...')
    })
    
    // Verificar conexão periodicamente
    setInterval(async () => {
      if (navigator.onLine) {
        try {
          // Verificar conexão básica
          const { error } = await supabase.from('driver_locations').select('count').limit(1)
          
          // Verificar conexão realtime
          const realtimeConnected = supabase.realtime && supabase.realtime.isConnected()
          
          if (error || !realtimeConnected) {
            console.warn('⚠️ Verificação periódica detectou problemas de conexão')
            
            // Tentar reconectar o realtime
            if (!realtimeConnected && supabase.realtime) {
              try {
                await supabase.realtime.connect()
                console.log('✅ Conexão Realtime restabelecida')
              } catch (e) {
                console.error('❌ Erro ao reconectar Realtime:', e)
              }
            }
          }
          
          // Tentar sincronizar operações pendentes
          if (pendingOperations.length > 0) {
            syncPendingOperations()
          }
        } catch (e) {
          console.warn('⚠️ Erro na verificação periódica:', e)
        }
      }
    }, 20000) // A cada 20 segundos
  }

  // Verificar conexão inicial
  checkInitialConnection()
  
  // Retornar funções de sincronização para uso no app
  return {
    addPendingOperation,
    syncPendingOperations,
    getPendingOperations: () => [...pendingOperations]
  }
}

// Render app immediately
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)

// Inicializar serviços específicos do driver app
const initializeDriverServices = async () => {
  try {
    // Configurar conexão robusta com Supabase
    const syncManager = setupDriverAppSupabaseConnection()
    
    // Disponibilizar globalmente para debugging
    if (typeof window !== 'undefined') {
      (window as any).__syncManager = syncManager
    }
    
    // Adicionar indicador visual de sincronização em desenvolvimento
    if (import.meta.env.DEV) {
      const syncIndicator = document.createElement('div')
      syncIndicator.id = 'sync-indicator'
      syncIndicator.style.position = 'fixed'
      syncIndicator.style.bottom = '10px'
      syncIndicator.style.right = '10px'
      syncIndicator.style.backgroundColor = 'rgba(0, 0, 0, 0.7)'
      syncIndicator.style.color = 'white'
      syncIndicator.style.padding = '8px 12px'
      syncIndicator.style.borderRadius = '4px'
      syncIndicator.style.fontSize = '12px'
      syncIndicator.style.zIndex = '9999'
      document.body.appendChild(syncIndicator)
      
      // Atualizar indicador a cada 2 segundos
      setInterval(() => {
        const pendingCount = syncManager.getPendingOperations().length
        syncIndicator.innerHTML = pendingCount > 0
          ? `🔄 Sincronizando: ${pendingCount} operações pendentes`
          : '✅ Sincronizado'
        syncIndicator.style.backgroundColor = pendingCount > 0
          ? 'rgba(33, 150, 243, 0.8)'
          : 'rgba(76, 175, 80, 0.8)'
      }, 2000)
    }

    console.log('✅ Serviços do Driver App inicializados com sucesso')

  } catch (error) {
    console.warn('⚠️ Falha na inicialização dos serviços:', error)
  }
}

// Inicializar serviços
initializeDriverServices()
