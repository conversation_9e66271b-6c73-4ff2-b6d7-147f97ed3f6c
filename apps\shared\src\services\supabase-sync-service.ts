/**
 * Serviço de sincronização avançado para o Supabase
 * Garante que os dados sejam enviados ao servidor mesmo em condições de rede instáveis
 */

import { supabase, supabaseHelpers } from './supabase-client';
import { supabaseError<PERSON>andler, SupabaseErrorType } from './supabase-error-handler';

// Interface para operação pendente
interface PendingOperation {
  id: string;
  table: string;
  operation: 'insert' | 'update' | 'upsert' | 'delete';
  data: any;
  options?: any;
  timestamp: number;
  retryCount: number;
  lastRetry: number;
  isSyncing: boolean;
  priority: number; // 1 = alta, 2 = média, 3 = baixa
}

// Configurações
const MAX_RETRIES = 10;
const RETRY_DELAY = 3000; // 3 segundos
const MAX_RETRY_DELAY = 60000; // 1 minuto
const HIGH_PRIORITY_TABLES = [
  'ride_requests',
  'driver_locations',
  'user_locations',
  'payments',
  'emergency_events'
];

/**
 * Classe para gerenciar sincronização com o Supabase
 */
class SupabaseSyncService {
  private static instance: SupabaseSyncService;
  private pendingOperations: PendingOperation[] = [];
  private syncInterval: any = null;
  private isOnline: boolean = typeof navigator !== 'undefined' ? navigator.onLine : true;
  private isSyncing: boolean = false;
  
  // Eventos
  private onSyncSuccessListeners: ((operation: PendingOperation) => void)[] = [];
  private onSyncErrorListeners: ((operation: PendingOperation, error: any) => void)[] = [];
  
  // Estatísticas
  private stats = {
    totalOperations: 0,
    successfulOperations: 0,
    failedOperations: 0,
    retriedOperations: 0
  };
  
  // Singleton pattern
  public static getInstance(): SupabaseSyncService {
    if (!SupabaseSyncService.instance) {
      SupabaseSyncService.instance = new SupabaseSyncService();
    }
    return SupabaseSyncService.instance;
  }
  
  private constructor() {
    this.loadFromStorage();
    this.setupEventListeners();
    this.startSyncInterval();
  }
  
  /**
   * Configurar event listeners
   */
  private setupEventListeners(): void {
    if (typeof window !== 'undefined') {
      window.addEventListener('online', this.handleOnline.bind(this));
      window.addEventListener('offline', this.handleOffline.bind(this));
      window.addEventListener('beforeunload', this.saveToStorage.bind(this));
      
      // Tentar sincronizar ao ganhar foco
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible' && this.isOnline) {
          this.sync();
        }
      });
    }
  }
  
  /**
   * Iniciar intervalo de sincronização
   */
  private startSyncInterval(): void {
    // Limpar intervalo existente
    this.stopSyncInterval();
    
    // Criar novo intervalo
    this.syncInterval = setInterval(() => {
      if (this.isOnline && this.pendingOperations.length > 0) {
        this.sync();
      }
    }, 5000); // Sincronizar a cada 5 segundos
    
    console.log('🔄 Intervalo de sincronização iniciado');
  }
  
  /**
   * Parar intervalo de sincronização
   */
  private stopSyncInterval(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      console.log('🛑 Intervalo de sincronização parado');
    }
  }
  
  /**
   * Lidar com evento online
   */
  private handleOnline(): void {
    console.log('🌐 Conexão restaurada, sincronizando operações pendentes...');
    this.isOnline = true;
    this.sync();
  }
  
  /**
   * Lidar com evento offline
   */
  private handleOffline(): void {
    console.log('🔌 Conexão perdida, pausando sincronização...');
    this.isOnline = false;
  }
  
  /**
   * Carregar operações pendentes do armazenamento local
   */
  private loadFromStorage(): void {
    if (typeof localStorage !== 'undefined') {
      try {
        const storedOperations = localStorage.getItem('supabase-pending-operations');
        if (storedOperations) {
          this.pendingOperations = JSON.parse(storedOperations);
          console.log(`📂 Carregadas ${this.pendingOperations.length} operações pendentes do armazenamento`);
        }
      } catch (error) {
        console.error('❌ Erro ao carregar operações pendentes:', error);
        this.pendingOperations = [];
      }
    }
  }
  
  /**
   * Salvar operações pendentes no armazenamento local
   */
  private saveToStorage(): void {
    if (typeof localStorage !== 'undefined') {
      try {
        localStorage.setItem(
          'supabase-pending-operations', 
          JSON.stringify(this.pendingOperations)
        );
      } catch (error) {
        console.error('❌ Erro ao salvar operações pendentes:', error);
      }
    }
  }
  
  /**
   * Determinar prioridade de uma operação
   */
  private getOperationPriority(table: string): number {
    if (HIGH_PRIORITY_TABLES.includes(table)) {
      return 1; // Alta prioridade
    }
    
    return 2; // Prioridade padrão
  }
  
  /**
   * Adicionar operação à fila de sincronização
   */
  public queueOperation(
    table: string,
    operation: 'insert' | 'update' | 'upsert' | 'delete',
    data: any,
    options: any = {}
  ): string {
    // Gerar ID único para a operação
    const id = `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    
    // Criar objeto de operação
    const pendingOperation: PendingOperation = {
      id,
      table,
      operation,
      data,
      options,
      timestamp: Date.now(),
      retryCount: 0,
      lastRetry: 0,
      isSyncing: false,
      priority: this.getOperationPriority(table)
    };
    
    // Adicionar à fila
    this.pendingOperations.push(pendingOperation);
    
    // Atualizar estatísticas
    this.stats.totalOperations++;
    
    // Salvar no armazenamento local
    this.saveToStorage();
    
    // Se estiver online, tentar sincronizar imediatamente operações de alta prioridade
    if (this.isOnline && pendingOperation.priority === 1) {
      this.syncOperation(pendingOperation);
    }
    
    return id;
  }
  
  /**
   * Adicionar inserção à fila
   */
  public insert(table: string, data: any, options: any = {}): string {
    return this.queueOperation(table, 'insert', data, options);
  }
  
  /**
   * Adicionar atualização à fila
   */
  public update(table: string, data: any, options: any = {}): string {
    return this.queueOperation(table, 'update', data, options);
  }
  
  /**
   * Adicionar upsert à fila
   */
  public upsert(table: string, data: any, options: any = {}): string {
    return this.queueOperation(table, 'upsert', data, options);
  }
  
  /**
   * Adicionar exclusão à fila
   */
  public delete(table: string, filter: any): string {
    return this.queueOperation(table, 'delete', filter);
  }
  
  /**
   * Sincronizar todas as operações pendentes
   */
  public async sync(): Promise<void> {
    // Verificar se já está sincronizando
    if (this.isSyncing) {
      return;
    }
    
    // Verificar se está online
    if (!this.isOnline) {
      console.log('🔌 Não é possível sincronizar: dispositivo offline');
      return;
    }
    
    // Verificar se há operações pendentes
    if (this.pendingOperations.length === 0) {
      return;
    }
    
    this.isSyncing = true;
    console.log(`🔄 Sincronizando ${this.pendingOperations.length} operações pendentes...`);
    
    try {
      // Verificar conexão com o Supabase
      const { connected } = await supabaseHelpers.checkConnection();
      
      if (!connected) {
        console.log('❌ Não é possível sincronizar: Supabase não está acessível');
        this.isSyncing = false;
        return;
      }
      
      // Ordenar operações por prioridade e timestamp
      const sortedOperations = [...this.pendingOperations]
        .sort((a, b) => {
          // Primeiro por prioridade
          if (a.priority !== b.priority) {
            return a.priority - b.priority;
          }
          
          // Depois por timestamp
          return a.timestamp - b.timestamp;
        })
        .filter(op => !op.isSyncing); // Ignorar operações que já estão sincronizando
      
      // Processar operações em paralelo, mas com limite
      const batchSize = 5; // Processar 5 operações por vez
      for (let i = 0; i < sortedOperations.length; i += batchSize) {
        const batch = sortedOperations.slice(i, i + batchSize);
        
        // Processar batch em paralelo
        await Promise.all(
          batch.map(operation => this.syncOperation(operation))
        );
      }
    } catch (error) {
      console.error('❌ Erro ao sincronizar operações:', error);
      supabaseErrorHandler.handleError(error, SupabaseErrorType.UNKNOWN);
    } finally {
      this.isSyncing = false;
      this.saveToStorage();
    }
  }
  
  /**
   * Sincronizar uma operação específica
   */
  private async syncOperation(operation: PendingOperation): Promise<boolean> {
    // Verificar se já está sincronizando
    if (operation.isSyncing) {
      return false;
    }
    
    // Marcar como sincronizando
    operation.isSyncing = true;
    operation.lastRetry = Date.now();
    operation.retryCount++;
    
    try {
      console.log(`🔄 Sincronizando operação ${operation.id} (${operation.operation} em ${operation.table})...`);
      
      let result;
      
      // Executar operação apropriada
      switch (operation.operation) {
        case 'insert':
          result = await supabase
            .from(operation.table)
            .insert(operation.data, { ...operation.options });
          break;
          
        case 'update':
          result = await supabase
            .from(operation.table)
            .update(operation.data, { ...operation.options });
          break;
          
        case 'upsert':
          result = await supabase
            .from(operation.table)
            .upsert(operation.data, { ...operation.options });
          break;
          
        case 'delete':
          result = await supabase
            .from(operation.table)
            .delete()
            .match(operation.data);
          break;
          
        default:
          throw new Error(`Operação não suportada: ${operation.operation}`);
      }
      
      // Verificar se houve erro
      if (result.error) {
        throw result.error;
      }
      
      // Operação concluída com sucesso
      console.log(`✅ Operação ${operation.id} sincronizada com sucesso`);
      
      // Remover da fila
      this.pendingOperations = this.pendingOperations.filter(op => op.id !== operation.id);
      
      // Atualizar estatísticas
      this.stats.successfulOperations++;
      
      // Notificar listeners
      this.notifySyncSuccess(operation);
      
      // Salvar no armazenamento local
      this.saveToStorage();
      
      return true;
    } catch (error) {
      console.error(`❌ Erro ao sincronizar operação ${operation.id}:`, error);
      
      // Registrar erro
      supabaseErrorHandler.handleError(error, SupabaseErrorType.DATABASE);
      
      // Atualizar estatísticas
      this.stats.retriedOperations++;
      
      // Notificar listeners
      this.notifySyncError(operation, error);
      
      // Verificar se atingiu o número máximo de tentativas
      if (operation.retryCount >= MAX_RETRIES) {
        console.warn(`⚠️ Operação ${operation.id} excedeu o número máximo de tentativas, removendo...`);
        
        // Remover da fila
        this.pendingOperations = this.pendingOperations.filter(op => op.id !== operation.id);
        
        // Atualizar estatísticas
        this.stats.failedOperations++;
        
        // Salvar no armazenamento local
        this.saveToStorage();
        
        return false;
      }
      
      // Marcar como não sincronizando para tentar novamente mais tarde
      operation.isSyncing = false;
      
      // Salvar no armazenamento local
      this.saveToStorage();
      
      // Agendar próxima tentativa com backoff exponencial
      const delay = Math.min(
        RETRY_DELAY * Math.pow(1.5, operation.retryCount),
        MAX_RETRY_DELAY
      );
      
      setTimeout(() => {
        if (this.isOnline) {
          this.syncOperation(operation);
        }
      }, delay);
      
      return false;
    }
  }
  
  /**
   * Notificar listeners sobre sincronização bem-sucedida
   */
  private notifySyncSuccess(operation: PendingOperation): void {
    this.onSyncSuccessListeners.forEach(listener => {
      try {
        listener(operation);
      } catch (error) {
        console.error('❌ Erro no listener de sucesso:', error);
      }
    });
  }
  
  /**
   * Notificar listeners sobre erro de sincronização
   */
  private notifySyncError(operation: PendingOperation, error: any): void {
    this.onSyncErrorListeners.forEach(listener => {
      try {
        listener(operation, error);
      } catch (listenerError) {
        console.error('❌ Erro no listener de erro:', listenerError);
      }
    });
  }
  
  /**
   * Adicionar listener para sincronização bem-sucedida
   */
  public onSyncSuccess(listener: (operation: PendingOperation) => void): () => void {
    this.onSyncSuccessListeners.push(listener);
    
    // Retornar função para remover listener
    return () => {
      this.onSyncSuccessListeners = this.onSyncSuccessListeners.filter(l => l !== listener);
    };
  }
  
  /**
   * Adicionar listener para erro de sincronização
   */
  public onSyncError(listener: (operation: PendingOperation, error: any) => void): () => void {
    this.onSyncErrorListeners.push(listener);
    
    // Retornar função para remover listener
    return () => {
      this.onSyncErrorListeners = this.onSyncErrorListeners.filter(l => l !== listener);
    };
  }
  
  /**
   * Obter operações pendentes
   */
  public getPendingOperations(): PendingOperation[] {
    return [...this.pendingOperations];
  }
  
  /**
   * Obter estatísticas
   */
  public getStats(): typeof this.stats {
    return { ...this.stats };
  }
  
  /**
   * Limpar operações pendentes
   */
  public clearPendingOperations(): void {
    this.pendingOperations = [];
    this.saveToStorage();
  }
  
  /**
   * Verificar se há operações pendentes para uma tabela específica
   */
  public hasPendingOperationsForTable(table: string): boolean {
    return this.pendingOperations.some(op => op.table === table);
  }
  
  /**
   * Verificar se está online
   */
  public isNetworkOnline(): boolean {
    return this.isOnline;
  }
}

// Exportar instância única
export const syncService = SupabaseSyncService.getInstance();

export default syncService;