// =====================================================
// EXECUTAR SCRIPT SQL COMPLETO NO SUPABASE USANDO PG
// =====================================================

import { createClient } from '@supabase/supabase-js'
import pg from 'pg'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

// Obter diretório atual
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Configurações do Supabase
const supabaseUrl = 'https://udquhavmgqtpkubrfzdm.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI'

// Criar cliente Supabase
const supabase = createClient(supabaseUrl, supabaseKey)

// Função para obter informações de conexão do Supabase
async function getConnectionInfo() {
  try {
    console.log('🔑 Obtendo informações de conexão do Supabase...')
    
    // Obter informações de conexão via API REST do Supabase
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      headers: {
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`
      }
    })
    
    if (!response.ok) {
      throw new Error(`Erro ao obter informações: ${response.statusText}`)
    }
    
    // Extrair host do URL do Supabase
    const host = new URL(supabaseUrl).hostname
    
    // Configuração de conexão para PostgreSQL
    // Nota: Estas são configurações padrão do Supabase, mas podem precisar ser ajustadas
    return {
      host: host,
      port: 5432,
      database: 'postgres',
      user: 'postgres',
      password: supabaseKey,
      ssl: { rejectUnauthorized: false }
    }
  } catch (error) {
    console.error('❌ Erro ao obter informações de conexão:', error)
    throw error
  }
}

// Função para ler o arquivo SQL
function readSqlFile() {
  try {
    // Caminho para o arquivo SQL
    const sqlFilePath = path.resolve(__dirname, '..', '..', 'database', 'migrations', 'complete_ride_system.sql')
    console.log(`📂 Lendo arquivo SQL: ${sqlFilePath}`)
    
    if (!fs.existsSync(sqlFilePath)) {
      throw new Error('Arquivo SQL não encontrado')
    }
    
    // Ler o conteúdo do arquivo
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8')
    console.log(`✅ Arquivo SQL lido com sucesso (${sqlContent.length} caracteres)`)
    return sqlContent
  } catch (error) {
    console.error('❌ Erro ao ler arquivo SQL:', error)
    throw error
  }
}

// Função para executar o script SQL usando pg
async function executeSqlWithPg() {
  let client = null
  
  try {
    console.log('🔌 Conectando ao PostgreSQL do Supabase...')
    
    // Obter informações de conexão
    const connectionInfo = await getConnectionInfo()
    
    // Criar cliente pg
    const { Pool } = pg
    const pool = new Pool(connectionInfo)
    client = await pool.connect()
    
    console.log('✅ Conectado ao PostgreSQL')
    
    // Ler o arquivo SQL
    const sqlContent = readSqlFile()
    
    // Dividir o script em comandos individuais
    // Esta é uma abordagem simplificada - scripts SQL complexos podem precisar de um parser mais robusto
    const commands = sqlContent
      .replace(/\r\n|\r|\n/g, ' ') // Substituir quebras de linha por espaços
      .replace(/--.*?$/gm, '') // Remover comentários de linha única
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remover comentários de múltiplas linhas
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0) // Remover comandos vazios
    
    console.log(`🔄 Executando ${commands.length} comandos SQL...`)
    
    // Executar cada comando individualmente
    for (let i = 0; i < commands.length; i++) {
      const command = commands[i]
      try {
        await client.query(command + ';')
        console.log(`✅ Comando ${i+1}/${commands.length} executado com sucesso`)
      } catch (error) {
        console.error(`❌ Erro ao executar comando ${i+1}/${commands.length}:`, error.message)
        console.error('Comando:', command)
        // Continuar com o próximo comando mesmo se houver erro
      }
    }
    
    console.log('✅ Script SQL executado com sucesso')
    return true
  } catch (error) {
    console.error('❌ Erro ao executar script SQL:', error)
    return false
  } finally {
    // Fechar conexão
    if (client) {
      client.release()
      console.log('🔌 Conexão com PostgreSQL fechada')
    }
  }
}

// Função para verificar campos após a execução
async function checkRideFields() {
  console.log('\n🔍 VERIFICANDO CAMPOS DA TABELA')
  console.log('='.repeat(50))

  try {
    // Testar campos um por um
    const possibleFields = [
      'id', 'user_id', 'driver_id',
      'pickup_address', 'destination_address',
      'pickup_latitude', 'pickup_longitude',
      'destination_latitude', 'destination_longitude',
      'distance', 'duration', 'estimated_price', 'final_price',
      'status', 'vehicle_type', 'payment_method',
      'created_at', 'updated_at', 'accepted_at', 'started_at', 'completed_at', 'cancelled_at'
    ]

    const existingFields = []
    
    for (const field of possibleFields) {
      try {
        const { error } = await supabase
          .from('ride_requests')
          .select(field)
          .limit(1)
        
        if (!error) {
          existingFields.push(field)
          console.log(`✅ ${field}`)
        } else {
          console.log(`❌ ${field} - ${error.message}`)
        }
      } catch (e) {
        console.log(`❌ ${field} - ${e.message}`)
      }
    }

    console.log('\n📊 CAMPOS EXISTENTES:')
    console.log(existingFields.join(', '))

    // Tentar inserção com campos mínimos
    console.log('\n🧪 TESTANDO INSERÇÃO COM CAMPOS MÍNIMOS...')
    
    const minimalData = {
      user_id: '9ad5afad-8d2d-423e-b6e9-15e3e5a6ddc3'
    }
    
    // Adicionar campos que existem
    if (existingFields.includes('pickup_address')) {
      minimalData.pickup_address = 'Teste Origin'
    }
    if (existingFields.includes('status')) {
      minimalData.status = 'pending'
    }
    if (existingFields.includes('pickup_latitude')) {
      minimalData.pickup_latitude = 40.7128
    }
    if (existingFields.includes('pickup_longitude')) {
      minimalData.pickup_longitude = -74.0060
    }
    if (existingFields.includes('destination_latitude')) {
      minimalData.destination_latitude = 34.0522
    }
    if (existingFields.includes('destination_longitude')) {
      minimalData.destination_longitude = -118.2437
    }
    if (existingFields.includes('destination_address')) {
      minimalData.destination_address = 'Los Angeles, CA'
    }
    
    console.log('📝 Dados para inserção:', minimalData)
    
    const { data: insertData, error: insertError } = await supabase
      .from('ride_requests')
      .insert(minimalData)
      .select('id')
      .single()
    
    if (insertError) {
      console.error('❌ Erro na inserção mínima:', insertError)
    } else {
      console.log('✅ Inserção mínima bem-sucedida:', insertData)
      
      // Limpar
      await supabase
        .from('ride_requests')
        .delete()
        .eq('id', insertData.id)
      
      console.log('🧹 Registro removido')
    }

  } catch (error) {
    console.error('💥 Erro:', error)
  }
}

// Função para gerar instruções para o usuário
function generateInstructions() {
  console.log('\n📋 INSTRUÇÕES ALTERNATIVAS (CASO O SCRIPT AUTOMÁTICO FALHE)')
  console.log('='.repeat(50))
  console.log('\n1. Acesse o Dashboard do Supabase: https://app.supabase.com')
  console.log('2. Selecione seu projeto')
  console.log('3. No menu lateral, clique em "SQL Editor"')
  console.log('4. Clique em "+ New Query"')
  console.log('5. Copie e cole o conteúdo do arquivo complete_ride_system.sql')
  console.log('   Localizado em: database/migrations/complete_ride_system.sql')
  console.log('6. Clique em "Run" para executar o script')
  console.log('7. Após a execução, execute este script novamente para verificar se os campos foram criados corretamente')
  
  // Ler o arquivo SQL para mostrar o caminho
  const sqlFilePath = path.resolve(__dirname, '..', '..', 'database', 'migrations', 'complete_ride_system.sql')
  console.log(`\n📂 Caminho completo do arquivo SQL: ${sqlFilePath}`)
  
  if (fs.existsSync(sqlFilePath)) {
    console.log('✅ Arquivo SQL encontrado!')
  } else {
    console.log('❌ Arquivo SQL não encontrado. Verifique o caminho.')
  }
}

// Executar verificação e gerar instruções
async function main() {
  console.log('🚀 EXECUÇÃO AUTOMÁTICA DO SCRIPT SQL')
  console.log('='.repeat(50))
  
  // Verificar campos atuais antes da execução
  console.log('\n📊 VERIFICANDO CAMPOS ANTES DA EXECUÇÃO:')
  await checkRideFields()
  
  // Executar o script SQL
  console.log('\n🔄 EXECUTANDO SCRIPT SQL:')
  const success = await executeSqlWithPg()
  
  if (success) {
    // Verificar campos após a execução
    console.log('\n📊 VERIFICANDO CAMPOS APÓS A EXECUÇÃO:')
    await checkRideFields()
  } else {
    // Gerar instruções para o usuário em caso de falha
    generateInstructions()
  }
}

main()
  .then(() => {
    console.log('\n🏁 PROCESSO CONCLUÍDO!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    generateInstructions()
    process.exit(1)
  })