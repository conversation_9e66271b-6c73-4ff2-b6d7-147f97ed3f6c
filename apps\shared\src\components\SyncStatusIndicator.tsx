import React, { useEffect, useState } from 'react';
import { syncService } from '../services/supabase-sync-service';
import { healthMonitor, HealthStatus } from '../services/supabase-health-monitor';

interface SyncStatusIndicatorProps {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'custom';
  className?: string;
  style?: React.CSSProperties;
  compact?: boolean;
  showDetails?: boolean;
  onSyncComplete?: () => void;
}

/**
 * Componente para exibir o status de sincronização
 */
export const SyncStatusIndicator: React.FC<SyncStatusIndicatorProps> = ({
  position = 'bottom-left',
  className = '',
  style = {},
  compact = true,
  showDetails = false,
  onSyncComplete
}) => {
  const [pendingOperations, setPendingOperations] = useState(syncService.getPendingOperations());
  const [healthCheck, setHealthCheck] = useState(healthMonitor.getLastCheck());
  const [expanded, setExpanded] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  
  // Monitorar operações pendentes
  useEffect(() => {
    // Função para atualizar estado
    const updateStatus = () => {
      setPendingOperations(syncService.getPendingOperations());
      
      // Se não houver operações pendentes, notificar
      if (syncService.getPendingOperations().length === 0 && pendingOperations.length > 0) {
        setLastSyncTime(new Date());
        if (onSyncComplete) {
          onSyncComplete();
        }
      }
    };
    
    // Listener para sucesso de sincronização
    const successUnsubscribe = syncService.onSyncSuccess(() => {
      updateStatus();
    });
    
    // Listener para erro de sincronização
    const errorUnsubscribe = syncService.onSyncError(() => {
      updateStatus();
    });
    
    // Listener para status de saúde
    const healthListenerId = healthMonitor.addListener((check) => {
      setHealthCheck(check);
    });
    
    // Intervalo para atualizar status
    const interval = setInterval(updateStatus, 2000);
    
    // Limpar listeners ao desmontar
    return () => {
      successUnsubscribe();
      errorUnsubscribe();
      healthMonitor.removeListener(healthListenerId);
      clearInterval(interval);
    };
  }, [pendingOperations.length, onSyncComplete]);
  
  // Verificar se está online
  const isOnline = navigator.onLine;
  
  // Verificar se está sincronizando
  const isSyncing = pendingOperations.length > 0;
  
  // Verificar se há problemas de saúde
  const hasHealthIssues = healthCheck?.status === HealthStatus.UNHEALTHY || 
                          healthCheck?.status === HealthStatus.OFFLINE;
  
  // Determinar ícone e status
  let icon: string;
  let statusText: string;
  let statusClass: string;
  
  if (!isOnline) {
    icon = '🔌';
    statusText = 'Offline';
    statusClass = 'bg-gray-100 border-gray-400 text-gray-800';
  } else if (hasHealthIssues) {
    icon = '⚠️';
    statusText = 'Problemas de conexão';
    statusClass = 'bg-red-100 border-red-400 text-red-800';
  } else if (isSyncing) {
    icon = '🔄';
    statusText = 'Sincronizando...';
    statusClass = 'bg-blue-100 border-blue-400 text-blue-800';
  } else {
    icon = '✅';
    statusText = 'Sincronizado';
    statusClass = 'bg-green-100 border-green-400 text-green-800';
  }
  
  // Determinar posicionamento
  let positionClasses = '';
  switch (position) {
    case 'top-right':
      positionClasses = 'fixed top-4 right-4';
      break;
    case 'top-left':
      positionClasses = 'fixed top-4 left-4';
      break;
    case 'bottom-right':
      positionClasses = 'fixed bottom-4 right-4';
      break;
    case 'bottom-left':
      positionClasses = 'fixed bottom-4 left-4';
      break;
    default:
      positionClasses = '';
  }
  
  // Renderizar versão compacta
  if (compact) {
    return (
      <div 
        className={`rounded-full w-8 h-8 flex items-center justify-center cursor-pointer shadow-sm ${statusClass} ${positionClasses} ${className}`}
        onClick={() => setExpanded(!expanded)}
        title={statusText}
        style={style}
      >
        <span role="img" aria-label={statusText}>
          {icon}
        </span>
        
        {/* Exibir badge com número de operações pendentes */}
        {isSyncing && (
          <div className="absolute -top-1 -right-1 bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
            {pendingOperations.length}
          </div>
        )}
        
        {/* Exibir popover com detalhes quando expandido */}
        {expanded && (
          <div className="absolute bottom-full right-0 mb-2 w-64 rounded-md shadow-lg bg-white p-3 text-sm">
            <div className="font-semibold mb-1">{statusText}</div>
            
            {isSyncing ? (
              <div className="text-gray-600 mb-2">
                {pendingOperations.length} operações pendentes
              </div>
            ) : (
              <div className="text-gray-600 mb-2">
                Todos os dados estão sincronizados
              </div>
            )}
            
            {lastSyncTime && (
              <div className="text-xs text-gray-500">
                Última sincronização: {lastSyncTime.toLocaleTimeString()}
              </div>
            )}
            
            {showDetails && isSyncing && (
              <div className="mt-2 text-xs text-gray-600">
                <div className="font-medium">Operações pendentes:</div>
                <ul className="list-disc list-inside">
                  {pendingOperations.slice(0, 3).map((op, index) => (
                    <li key={index}>
                      {op.operation} em {op.table}
                    </li>
                  ))}
                  {pendingOperations.length > 3 && (
                    <li>
                      + {pendingOperations.length - 3} operações
                    </li>
                  )}
                </ul>
              </div>
            )}
            
            {/* Botão para forçar sincronização */}
            {isSyncing && isOnline && !hasHealthIssues && (
              <button 
                className="mt-2 px-2 py-1 text-xs rounded bg-blue-100 text-blue-800 border border-blue-400 hover:bg-blue-200"
                onClick={(e) => {
                  e.stopPropagation();
                  syncService.sync();
                }}
              >
                Sincronizar agora
              </button>
            )}
          </div>
        )}
      </div>
    );
  }
  
  // Renderizar versão completa
  return (
    <div 
      className={`rounded-md p-3 ${statusClass} ${positionClasses} ${className}`}
      style={style}
    >
      <div className="flex items-center">
        <span role="img" aria-label={statusText} className="mr-2">
          {icon}
        </span>
        <div>
          <div className="font-medium">{statusText}</div>
          {isSyncing ? (
            <div className="text-sm">{pendingOperations.length} operações pendentes</div>
          ) : (
            <div className="text-sm">Todos os dados estão sincronizados</div>
          )}
        </div>
      </div>
      
      {showDetails && (
        <div className="mt-2 text-xs border-t border-opacity-20 pt-2">
          {lastSyncTime && (
            <div>Última sincronização: {lastSyncTime.toLocaleTimeString()}</div>
          )}
          
          {isSyncing && (
            <div className="mt-1">
              <div className="font-medium">Operações pendentes:</div>
              <ul className="list-disc list-inside">
                {pendingOperations.slice(0, 5).map((op, index) => (
                  <li key={index}>
                    {op.operation} em {op.table}
                  </li>
                ))}
                {pendingOperations.length > 5 && (
                  <li>
                    + {pendingOperations.length - 5} operações
                  </li>
                )}
              </ul>
            </div>
          )}
          
          {/* Botão para forçar sincronização */}
          {isSyncing && isOnline && !hasHealthIssues && (
            <button 
              className="mt-2 px-2 py-1 text-xs rounded bg-blue-100 text-blue-800 border border-blue-400 hover:bg-blue-200"
              onClick={() => syncService.sync()}
            >
              Sincronizar agora
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default SyncStatusIndicator;