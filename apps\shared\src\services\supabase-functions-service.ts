/**
 * Serviço para comunicação confiável com funções Edge do Supabase
 * Gerencia chamadas de funções com retry automático e tratamento de erros
 */

import { supabase } from './supabase-client';
import { supabaseErrorHandler, SupabaseErrorType } from './supabase-error-handler';

// Opções para chamada de função
export interface FunctionCallOptions {
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retryCount?: number;
  retryDelay?: number;
  criticalOperation?: boolean;
}

// Resultado da chamada de função
export interface FunctionCallResult<T = any> {
  data: T | null;
  error: any | null;
  statusCode: number | null;
  duration: number;
  retries: number;
}

/**
 * Classe para gerenciar chamadas de funções do Supabase
 */
class SupabaseFunctionsService {
  private static instance: SupabaseFunctionsService;
  private defaultTimeout = 30000; // 30 segundos
  private defaultRetryCount = 3;
  private defaultRetryDelay = 1000; // 1 segundo
  
  // Cache de funções
  private functionCache: Map<string, { data: any, timestamp: number, ttl: number }> = new Map();
  
  // Singleton pattern
  public static getInstance(): SupabaseFunctionsService {
    if (!SupabaseFunctionsService.instance) {
      SupabaseFunctionsService.instance = new SupabaseFunctionsService();
    }
    return SupabaseFunctionsService.instance;
  }
  
  private constructor() {}
  
  /**
   * Chamar função Edge do Supabase com retry automático
   */
  public async callFunction<T = any>(
    functionName: string,
    options: FunctionCallOptions = {}
  ): Promise<FunctionCallResult<T>> {
    const {
      headers = {},
      body = {},
      timeout = this.defaultTimeout,
      retryCount = this.defaultRetryCount,
      retryDelay = this.defaultRetryDelay,
      criticalOperation = false
    } = options;
    
    // Adicionar timestamp para evitar cache
    const bodyWithTimestamp = {
      ...body,
      _t: Date.now()
    };
    
    // Estatísticas para resultado
    let totalRetries = 0;
    const startTime = Date.now();
    
    // Criar função para fazer a requisição
    const makeRequest = async (): Promise<FunctionCallResult<T>> => {
      try {
        // Adicionar timeout à requisição
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        // Fazer requisição
        const { data, error } = await supabase.functions.invoke<T>(
          functionName,
          {
            body: bodyWithTimestamp,
            headers,
            signal: controller.signal
          }
        );
        
        // Limpar timeout
        clearTimeout(timeoutId);
        
        // Se não houver erro, retornar dados
        if (!error) {
          return {
            data,
            error: null,
            statusCode: 200,
            duration: Date.now() - startTime,
            retries: totalRetries
          };
        }
        
        // Tratar erro
        const statusCode = error.status || error.statusCode || 500;
        
        // Lançar erro para ser tratado pelo retry
        throw {
          message: error.message || 'Erro desconhecido',
          statusCode,
          error
        };
      } catch (error) {
        // Registrar erro
        supabaseErrorHandler.handleError(error, SupabaseErrorType.FUNCTIONS);
        
        // Determinar se deve tentar novamente
        const statusCode = error.statusCode || error.status || 500;
        const shouldRetry = 
          totalRetries < retryCount && 
          (statusCode >= 500 || statusCode === 429 || statusCode === 0); // 0 = erro de rede
        
        if (shouldRetry) {
          totalRetries++;
          
          // Calcular delay com backoff exponencial
          const delay = retryDelay * Math.pow(1.5, totalRetries - 1);
          
          console.log(`⚠️ Erro ao chamar função ${functionName}, tentando novamente (${totalRetries}/${retryCount}) em ${delay}ms...`);
          
          // Aguardar antes de tentar novamente
          await new Promise(resolve => setTimeout(resolve, delay));
          
          // Tentar novamente
          return makeRequest();
        }
        
        // Não tentar novamente, retornar erro
        return {
          data: null,
          error,
          statusCode,
          duration: Date.now() - startTime,
          retries: totalRetries
        };
      }
    };
    
    // Para operações críticas, tentar ainda mais vezes
    if (criticalOperation) {
      try {
        // Primeira tentativa com configurações normais
        const result = await makeRequest();
        
        // Se falhou e é uma operação crítica, tentar novamente com maior timeout
        if (result.error) {
          console.warn(`⚠️ Operação crítica falhou: ${functionName}, tentando com timeout maior...`);
          
          // Tentar novamente com timeout maior
          options.timeout = timeout * 2;
          options.retryCount = retryCount * 2;
          
          return this.callFunction<T>(functionName, options);
        }
        
        return result;
      } catch (error) {
        // Registrar erro crítico
        console.error(`❌ Falha crítica na função ${functionName}:`, error);
        
        return {
          data: null,
          error,
          statusCode: 500,
          duration: Date.now() - startTime,
          retries: totalRetries
        };
      }
    }
    
    // Executar requisição
    return makeRequest();
  }
  
  /**
   * Chamar função com cache
   */
  public async callFunctionWithCache<T = any>(
    functionName: string,
    options: FunctionCallOptions & { ttl?: number } = {}
  ): Promise<FunctionCallResult<T>> {
    const { ttl = 60000, ...restOptions } = options; // 1 minuto por padrão
    
    // Gerar chave de cache
    const cacheKey = this.generateCacheKey(functionName, restOptions.body || {});
    
    // Verificar cache
    const cached = this.functionCache.get(cacheKey);
    if (cached && cached.timestamp + cached.ttl > Date.now()) {
      console.log(`📦 Usando cache para função ${functionName}`);
      
      return {
        data: cached.data,
        error: null,
        statusCode: 200,
        duration: 0,
        retries: 0
      };
    }
    
    // Chamar função
    const result = await this.callFunction<T>(functionName, restOptions);
    
    // Se não houver erro, adicionar ao cache
    if (!result.error && result.data) {
      this.functionCache.set(cacheKey, {
        data: result.data,
        timestamp: Date.now(),
        ttl
      });
    }
    
    return result;
  }
  
  /**
   * Gerar chave de cache
   */
  private generateCacheKey(functionName: string, body: any): string {
    // Ordenar chaves para garantir que objetos iguais geram a mesma chave
    const sortedBody = Object.keys(body)
      .sort()
      .reduce((acc, key) => {
        // Ignorar campos que começam com underscore (como _t)
        if (!key.startsWith('_')) {
          acc[key] = body[key];
        }
        return acc;
      }, {} as any);
    
    return `${functionName}:${JSON.stringify(sortedBody)}`;
  }
  
  /**
   * Limpar cache
   */
  public clearCache(): void {
    this.functionCache.clear();
  }
  
  /**
   * Limpar cache para uma função específica
   */
  public clearCacheForFunction(functionName: string): void {
    for (const [key] of this.functionCache.entries()) {
      if (key.startsWith(`${functionName}:`)) {
        this.functionCache.delete(key);
      }
    }
  }
  
  /**
   * Definir timeout padrão
   */
  public setDefaultTimeout(timeout: number): void {
    this.defaultTimeout = timeout;
  }
  
  /**
   * Definir retry count padrão
   */
  public setDefaultRetryCount(retryCount: number): void {
    this.defaultRetryCount = retryCount;
  }
  
  /**
   * Definir retry delay padrão
   */
  public setDefaultRetryDelay(retryDelay: number): void {
    this.defaultRetryDelay = retryDelay;
  }
}

// Exportar instância única
export const functionsService = SupabaseFunctionsService.getInstance();

export default functionsService;