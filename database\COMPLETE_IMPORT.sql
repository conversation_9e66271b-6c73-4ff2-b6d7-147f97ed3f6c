-- =====================================================
-- MOBIDRIVE - IMPORTAÇÃO COMPLETA DO BANCO DE DADOS
-- =====================================================
-- Execute este script completo no SQL Editor do Supabase Dashboard
-- URL: https://supabase.com/dashboard/project/udquhavmgqtpkubrfzdm/editor
-- =====================================================

-- Limpar tabelas existentes se necessário (CUIDADO: Remove todos os dados!)
-- DROP TABLE IF EXISTS driver_notifications CASCADE;
-- DROP TABLE IF EXISTS ride_matching_history CASCADE;
-- DROP TABLE IF EXISTS driver_profiles CASCADE;
-- DROP TABLE IF EXISTS driver_locations CASCADE;

-- =====================================================
-- 1. CRIAR TABELA DRIVER_PROFILES
-- =====================================================

CREATE TABLE IF NOT EXISTS driver_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    vehicle_type VARCHAR(50) DEFAULT 'economy', -- economy, comfort, premium, moto
    vehicle_make VARCHAR(100),
    vehicle_model VARCHAR(100),
    vehicle_year INTEGER,
    vehicle_color VARCHAR(50),
    vehicle_plate VARCHAR(20),
    license_number VARCHAR(50),
    license_expiry DATE,
    rating DECIMAL(3, 2) DEFAULT 4.5,
    total_rides INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- =====================================================
-- 2. CRIAR TABELA DRIVER_LOCATIONS
-- =====================================================

CREATE TABLE IF NOT EXISTS driver_locations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    heading DECIMAL(5, 2), -- Direction in degrees (0-360)
    speed DECIMAL(5, 2), -- Speed in km/h
    accuracy DECIMAL(8, 2), -- GPS accuracy in meters
    is_available BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- =====================================================
-- 3. CRIAR TABELA DRIVER_NOTIFICATIONS
-- =====================================================

CREATE TABLE IF NOT EXISTS driver_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    driver_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    ride_id UUID REFERENCES ride_requests(id) ON DELETE CASCADE,
    notification_type VARCHAR(50) NOT NULL, -- 'ride_request', 'ride_cancelled', 'payment_received'
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT false,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 4. CRIAR TABELA RIDE_MATCHING_HISTORY
-- =====================================================

CREATE TABLE IF NOT EXISTS ride_matching_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    ride_id UUID NOT NULL REFERENCES ride_requests(id) ON DELETE CASCADE,
    driver_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    matching_score DECIMAL(5, 2),
    distance_km DECIMAL(8, 3),
    eta_minutes INTEGER,
    notification_sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    response_received_at TIMESTAMP WITH TIME ZONE,
    response_type VARCHAR(20), -- 'accepted', 'declined', 'timeout'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 5. CRIAR ÍNDICES PARA PERFORMANCE
-- =====================================================

-- Índices para driver_profiles
CREATE INDEX IF NOT EXISTS idx_driver_profiles_user_id ON driver_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_driver_profiles_active ON driver_profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_driver_profiles_vehicle_type ON driver_profiles(vehicle_type);
CREATE INDEX IF NOT EXISTS idx_driver_profiles_verified ON driver_profiles(is_verified);

-- Índices para driver_locations
CREATE INDEX IF NOT EXISTS idx_driver_locations_user_id ON driver_locations(user_id);
CREATE INDEX IF NOT EXISTS idx_driver_locations_active ON driver_locations(is_active, is_available);
CREATE INDEX IF NOT EXISTS idx_driver_locations_coords ON driver_locations(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_driver_locations_updated ON driver_locations(updated_at);

-- Índices para driver_notifications
CREATE INDEX IF NOT EXISTS idx_driver_notifications_driver_id ON driver_notifications(driver_id);
CREATE INDEX IF NOT EXISTS idx_driver_notifications_ride_id ON driver_notifications(ride_id);
CREATE INDEX IF NOT EXISTS idx_driver_notifications_unread ON driver_notifications(driver_id, is_read);
CREATE INDEX IF NOT EXISTS idx_driver_notifications_expires ON driver_notifications(expires_at);

-- Índices para ride_matching_history
CREATE INDEX IF NOT EXISTS idx_ride_matching_history_ride_id ON ride_matching_history(ride_id);
CREATE INDEX IF NOT EXISTS idx_ride_matching_history_driver_id ON ride_matching_history(driver_id);
CREATE INDEX IF NOT EXISTS idx_ride_matching_history_created ON ride_matching_history(created_at);

-- =====================================================
-- 6. CRIAR FUNÇÕES DE TRIGGER
-- =====================================================

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- =====================================================
-- 7. CRIAR TRIGGERS
-- =====================================================

-- Triggers para updated_at
DROP TRIGGER IF EXISTS update_driver_profiles_updated_at ON driver_profiles;
CREATE TRIGGER update_driver_profiles_updated_at 
    BEFORE UPDATE ON driver_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_driver_locations_updated_at ON driver_locations;
CREATE TRIGGER update_driver_locations_updated_at 
    BEFORE UPDATE ON driver_locations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 8. HABILITAR ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Habilitar RLS em todas as tabelas
ALTER TABLE driver_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE ride_matching_history ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 9. CRIAR POLÍTICAS RLS
-- =====================================================

-- Políticas para driver_profiles
DROP POLICY IF EXISTS "Anyone can view active driver profiles" ON driver_profiles;
CREATE POLICY "Anyone can view active driver profiles" ON driver_profiles
    FOR SELECT USING (is_active = true);

DROP POLICY IF EXISTS "Drivers can insert their own profile" ON driver_profiles;
CREATE POLICY "Drivers can insert their own profile" ON driver_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Drivers can update their own profile" ON driver_profiles;
CREATE POLICY "Drivers can update their own profile" ON driver_profiles
    FOR UPDATE USING (auth.uid() = user_id);

-- Políticas para driver_locations
DROP POLICY IF EXISTS "Drivers can view all active driver locations" ON driver_locations;
CREATE POLICY "Drivers can view all active driver locations" ON driver_locations
    FOR SELECT USING (is_active = true);

DROP POLICY IF EXISTS "Drivers can insert their own location" ON driver_locations;
CREATE POLICY "Drivers can insert their own location" ON driver_locations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Drivers can update their own location" ON driver_locations;
CREATE POLICY "Drivers can update their own location" ON driver_locations
    FOR UPDATE USING (auth.uid() = user_id);

-- Políticas para driver_notifications
DROP POLICY IF EXISTS "Drivers can view their own notifications" ON driver_notifications;
CREATE POLICY "Drivers can view their own notifications" ON driver_notifications
    FOR SELECT USING (auth.uid() = driver_id);

DROP POLICY IF EXISTS "System can insert notifications" ON driver_notifications;
CREATE POLICY "System can insert notifications" ON driver_notifications
    FOR INSERT WITH CHECK (true);

DROP POLICY IF EXISTS "Drivers can update their own notifications" ON driver_notifications;
CREATE POLICY "Drivers can update their own notifications" ON driver_notifications
    FOR UPDATE USING (auth.uid() = driver_id);

-- Políticas para ride_matching_history
DROP POLICY IF EXISTS "Users can view their own matching history" ON ride_matching_history;
CREATE POLICY "Users can view their own matching history" ON ride_matching_history
    FOR SELECT USING (auth.uid() = driver_id);

DROP POLICY IF EXISTS "System can insert matching history" ON ride_matching_history;
CREATE POLICY "System can insert matching history" ON ride_matching_history
    FOR INSERT WITH CHECK (true);

-- =====================================================
-- 10. CONCEDER PERMISSÕES
-- =====================================================

-- Conceder permissões para usuários autenticados
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON driver_profiles TO authenticated;
GRANT ALL ON driver_locations TO authenticated;
GRANT ALL ON driver_notifications TO authenticated;
GRANT ALL ON ride_matching_history TO authenticated;
GRANT EXECUTE ON FUNCTION update_updated_at_column TO authenticated;

-- =====================================================
-- 11. COMENTÁRIOS PARA DOCUMENTAÇÃO
-- =====================================================

COMMENT ON TABLE driver_profiles IS 'Perfis de motoristas com informações do veículo e documentação';
COMMENT ON TABLE driver_locations IS 'Localização em tempo real dos motoristas ativos';
COMMENT ON TABLE driver_notifications IS 'Notificações persistentes para motoristas';
COMMENT ON TABLE ride_matching_history IS 'Histórico de tentativas de matching para analytics';

-- =====================================================
-- 12. VERIFICAÇÃO FINAL
-- =====================================================

-- Verificar se todas as tabelas foram criadas
DO $$
BEGIN
    RAISE NOTICE '✅ Verificando tabelas criadas...';
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'driver_profiles') THEN
        RAISE NOTICE '✅ driver_profiles: OK';
    ELSE
        RAISE NOTICE '❌ driver_profiles: FALHOU';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'driver_locations') THEN
        RAISE NOTICE '✅ driver_locations: OK';
    ELSE
        RAISE NOTICE '❌ driver_locations: FALHOU';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'driver_notifications') THEN
        RAISE NOTICE '✅ driver_notifications: OK';
    ELSE
        RAISE NOTICE '❌ driver_notifications: FALHOU';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'ride_matching_history') THEN
        RAISE NOTICE '✅ ride_matching_history: OK';
    ELSE
        RAISE NOTICE '❌ ride_matching_history: FALHOU';
    END IF;
    
    RAISE NOTICE '🎉 Importação completa finalizada!';
END $$;
