import { supabase } from '../lib/supabase'
import { driverLocationService } from './DriverLocationService'

export interface PricingFactors {
  baseFare: number
  distanceRate: number
  timeRate: number
  demandMultiplier: number
  weatherMultiplier: number
  timeOfDayMultiplier: number
  routeComplexityMultiplier: number
  driverAvailabilityMultiplier: number
}

export interface PriceEstimate {
  basePrice: number
  finalPrice: number
  surge: number
  breakdown: {
    baseFare: number
    distanceCost: number
    timeCost: number
    surgeAmount: number
  }
  factors: PricingFactors
  explanation: string[]
}

export interface DemandData {
  activeRides: number
  availableDrivers: number
  pendingRequests: number
  averageWaitTime: number
  region: string
}

export class DynamicPricingService {
  private readonly BASE_RATES = {
    economy: { base: 5.00, perKm: 1.80, perMin: 0.25 },
    comfort: { base: 8.00, perKm: 2.50, perMin: 0.35 },
    premium: { base: 12.00, perKm: 3.50, perMin: 0.50 },
    moto: { base: 3.50, perKm: 1.20, perMin: 0.20 }
  }

  private readonly MINIMUM_FARES = {
    economy: 6.00,
    comfort: 9.00,
    premium: 15.00,
    moto: 5.00
  }

  /**
   * Calculate dynamic price for a ride
   */
  async calculatePrice(
    vehicleType: string,
    distanceKm: number,
    durationMinutes: number,
    pickupLat: number,
    pickupLng: number,
    destinationLat?: number,
    destinationLng?: number
  ): Promise<PriceEstimate> {
    console.log(`💰 Calculating dynamic price for ${vehicleType}`)
    console.log(`📏 Distance: ${distanceKm}km, Duration: ${durationMinutes}min`)

    // Get base rates for vehicle type
    const rates = this.BASE_RATES[vehicleType as keyof typeof this.BASE_RATES] || this.BASE_RATES.economy
    const minimumFare = this.MINIMUM_FARES[vehicleType as keyof typeof this.MINIMUM_FARES] || this.MINIMUM_FARES.economy

    // Calculate base price
    const baseFare = rates.base
    const distanceCost = distanceKm * rates.perKm
    const timeCost = durationMinutes * rates.perMin
    const basePrice = baseFare + distanceCost + timeCost

    // Get dynamic factors
    const factors = await this.calculatePricingFactors(
      pickupLat, pickupLng, destinationLat, destinationLng, vehicleType
    )

    // Apply all multipliers
    let finalPrice = basePrice
    finalPrice *= factors.demandMultiplier
    finalPrice *= factors.weatherMultiplier
    finalPrice *= factors.timeOfDayMultiplier
    finalPrice *= factors.routeComplexityMultiplier
    finalPrice *= factors.driverAvailabilityMultiplier

    // Ensure minimum fare
    finalPrice = Math.max(finalPrice, minimumFare)

    // Calculate surge
    const surge = finalPrice / basePrice
    const surgeAmount = finalPrice - basePrice

    // Generate explanation
    const explanation = this.generatePriceExplanation(factors, surge)

    const estimate: PriceEstimate = {
      basePrice,
      finalPrice,
      surge,
      breakdown: {
        baseFare,
        distanceCost,
        timeCost,
        surgeAmount
      },
      factors,
      explanation
    }

    console.log(`💵 Price calculated: R$ ${finalPrice.toFixed(2)} (${surge.toFixed(1)}x surge)`)
    return estimate
  }

  /**
   * Calculate all pricing factors
   */
  private async calculatePricingFactors(
    pickupLat: number,
    pickupLng: number,
    destinationLat?: number,
    destinationLng?: number,
    vehicleType: string = 'economy'
  ): Promise<PricingFactors> {
    const [
      demandMultiplier,
      weatherMultiplier,
      timeOfDayMultiplier,
      routeComplexityMultiplier,
      driverAvailabilityMultiplier
    ] = await Promise.all([
      this.calculateDemandMultiplier(pickupLat, pickupLng),
      this.calculateWeatherMultiplier(),
      this.calculateTimeOfDayMultiplier(),
      this.calculateRouteComplexityMultiplier(pickupLat, pickupLng, destinationLat, destinationLng),
      this.calculateDriverAvailabilityMultiplier(pickupLat, pickupLng, vehicleType)
    ])

    return {
      baseFare: this.BASE_RATES[vehicleType as keyof typeof this.BASE_RATES]?.base || this.BASE_RATES.economy.base,
      distanceRate: this.BASE_RATES[vehicleType as keyof typeof this.BASE_RATES]?.perKm || this.BASE_RATES.economy.perKm,
      timeRate: this.BASE_RATES[vehicleType as keyof typeof this.BASE_RATES]?.perMin || this.BASE_RATES.economy.perMin,
      demandMultiplier,
      weatherMultiplier,
      timeOfDayMultiplier,
      routeComplexityMultiplier,
      driverAvailabilityMultiplier
    }
  }

  /**
   * Calculate demand-based multiplier
   */
  private async calculateDemandMultiplier(lat: number, lng: number): Promise<number> {
    try {
      const demandData = await this.getDemandData(lat, lng)
      
      // Calculate supply/demand ratio
      const supplyDemandRatio = demandData.availableDrivers / Math.max(1, demandData.pendingRequests + demandData.activeRides)
      
      let multiplier = 1.0

      if (supplyDemandRatio < 0.5) {
        // Very high demand, low supply
        multiplier = 2.0
      } else if (supplyDemandRatio < 0.8) {
        // High demand
        multiplier = 1.5
      } else if (supplyDemandRatio < 1.2) {
        // Balanced
        multiplier = 1.0
      } else {
        // Low demand, high supply
        multiplier = 0.9
      }

      // Factor in average wait time
      if (demandData.averageWaitTime > 10) {
        multiplier *= 1.3
      } else if (demandData.averageWaitTime > 5) {
        multiplier *= 1.1
      }

      return Math.min(3.0, Math.max(0.8, multiplier)) // Cap between 0.8x and 3.0x
    } catch (error) {
      console.error('Error calculating demand multiplier:', error)
      return 1.0
    }
  }

  /**
   * Get demand data for a specific area
   */
  private async getDemandData(lat: number, lng: number): Promise<DemandData> {
    const radius = 0.02 // ~2km radius

    try {
      // ⚠️ TABELA LIMITADA - Usar dados mock temporariamente
      console.warn('⚠️ DynamicPricingService: Usando dados mock devido a schema limitado');

      // Mock data for demand calculation
      const activeRides = []
      const pendingRequests = []

      // ⚠️ MOCK DATA - Schema limitado
      const availableDrivers = []

      // Calculate average wait time
      const avgWaitTime = pendingRequests?.length ? 
        pendingRequests.reduce((sum, req) => {
          const waitTime = (Date.now() - new Date(req.created_at).getTime()) / (1000 * 60)
          return sum + waitTime
        }, 0) / pendingRequests.length : 0

      return {
        activeRides: activeRides?.length || 0,
        availableDrivers: availableDrivers?.length || 0,
        pendingRequests: pendingRequests?.length || 0,
        averageWaitTime: avgWaitTime,
        region: `${lat.toFixed(3)},${lng.toFixed(3)}`
      }
    } catch (error) {
      console.error('Error getting demand data:', error)
      return {
        activeRides: 0,
        availableDrivers: 5,
        pendingRequests: 0,
        averageWaitTime: 0,
        region: 'unknown'
      }
    }
  }

  /**
   * Calculate weather-based multiplier
   */
  private async calculateWeatherMultiplier(): Promise<number> {
    // In a real app, you'd integrate with a weather API
    // For now, simulate based on time and randomness
    const hour = new Date().getHours()
    const random = Math.random()

    // Simulate rain during certain hours
    if ((hour >= 16 && hour <= 19) && random < 0.3) {
      return 1.3 // Rain surge
    }

    // Simulate extreme weather
    if (random < 0.05) {
      return 1.5 // Extreme weather
    }

    return 1.0 // Normal weather
  }

  /**
   * Calculate time-of-day multiplier
   */
  private calculateTimeOfDayMultiplier(): number {
    const hour = new Date().getHours()
    const dayOfWeek = new Date().getDay()

    // Weekend nights
    if ((dayOfWeek === 5 || dayOfWeek === 6) && (hour >= 22 || hour <= 3)) {
      return 1.4
    }

    // Rush hours
    if ((hour >= 7 && hour <= 9) || (hour >= 17 && hour <= 19)) {
      return 1.2
    }

    // Late night
    if (hour >= 23 || hour <= 5) {
      return 1.3
    }

    // Normal hours
    return 1.0
  }

  /**
   * Calculate route complexity multiplier
   */
  private calculateRouteComplexityMultiplier(
    pickupLat: number,
    pickupLng: number,
    destinationLat?: number,
    destinationLng?: number
  ): number {
    if (!destinationLat || !destinationLng) return 1.0

    // Calculate if route crosses city center or complex areas
    const cityCenter = { lat: -23.5505, lng: -46.6333 } // São Paulo center
    
    // Check if route crosses downtown area
    const crossesDowntown = this.routeCrossesArea(
      pickupLat, pickupLng, destinationLat, destinationLng,
      cityCenter.lat, cityCenter.lng, 0.02
    )

    if (crossesDowntown) {
      return 1.1 // 10% increase for complex routes
    }

    return 1.0
  }

  /**
   * Calculate driver availability multiplier
   */
  private async calculateDriverAvailabilityMultiplier(
    lat: number,
    lng: number,
    vehicleType: string
  ): Promise<number> {
    try {
      // Get available drivers of specific type in area
      // Refactored to use DriverLocationService and client-side filtering
      const nearbyDrivers = await driverLocationService.findNearbyDrivers(lat, lng, 2) // 2km radius

      const availableCount = nearbyDrivers.filter(driver => driver.vehicle_type === vehicleType).length

      if (availableCount === 0) {
        return 1.5 // No drivers of this type available
      } else if (availableCount <= 2) {
        return 1.2 // Very few drivers
      } else if (availableCount <= 5) {
        return 1.0 // Normal availability
      } else {
        return 0.95 // High availability
      }
    } catch (error) {
      console.error('Error calculating driver availability:', error)
      return 1.0
    }
  }

  /**
   * Check if route crosses a specific area
   */
  private routeCrossesArea(
    lat1: number, lng1: number,
    lat2: number, lng2: number,
    areaLat: number, areaLng: number,
    areaRadius: number
  ): boolean {
    // Simple check if route bounding box intersects with area
    const minLat = Math.min(lat1, lat2)
    const maxLat = Math.max(lat1, lat2)
    const minLng = Math.min(lng1, lng2)
    const maxLng = Math.max(lng1, lng2)

    return (
      areaLat >= minLat - areaRadius && areaLat <= maxLat + areaRadius &&
      areaLng >= minLng - areaRadius && areaLng <= maxLng + areaRadius
    )
  }

  /**
   * Generate human-readable price explanation
   */
  private generatePriceExplanation(factors: PricingFactors, surge: number): string[] {
    const explanations: string[] = []

    if (surge > 1.5) {
      explanations.push('🔥 Alta demanda na região')
    } else if (surge > 1.2) {
      explanations.push('📈 Demanda aumentada')
    }

    if (factors.timeOfDayMultiplier > 1.1) {
      const hour = new Date().getHours()
      if (hour >= 7 && hour <= 9 || hour >= 17 && hour <= 19) {
        explanations.push('🚗 Horário de pico')
      } else if (hour >= 22 || hour <= 5) {
        explanations.push('🌙 Tarifa noturna')
      }
    }

    if (factors.weatherMultiplier > 1.1) {
      explanations.push('🌧️ Condições climáticas')
    }

    if (factors.driverAvailabilityMultiplier > 1.1) {
      explanations.push('👥 Poucos motoristas disponíveis')
    }

    if (factors.routeComplexityMultiplier > 1.0) {
      explanations.push('🏙️ Rota complexa')
    }

    if (explanations.length === 0) {
      explanations.push('💚 Preço normal')
    }

    return explanations
  }

  /**
   * Get surge areas for display on map
   */
  async getSurgeAreas(): Promise<Array<{
    center: [number, number]
    radius: number
    surge: number
    color: string
  }>> {
    // Define key areas in São Paulo
    const areas = [
      { lat: -23.5505, lng: -46.6333, name: 'Centro' },
      { lat: -23.5475, lng: -46.6361, name: 'República' },
      { lat: -23.5629, lng: -46.6544, name: 'Vila Madalena' },
      { lat: -23.5505, lng: -46.6333, name: 'Paulista' }
    ]

    const surgeAreas = await Promise.all(
      areas.map(async area => {
        const demandData = await this.getDemandData(area.lat, area.lng)
        const supplyDemandRatio = demandData.availableDrivers / Math.max(1, demandData.pendingRequests + demandData.activeRides)
        
        let surge = 1.0
        if (supplyDemandRatio < 0.5) surge = 2.0
        else if (supplyDemandRatio < 0.8) surge = 1.5
        else if (supplyDemandRatio < 1.2) surge = 1.0
        else surge = 0.9

        let color = '#22c55e' // Green for normal
        if (surge >= 2.0) color = '#ef4444' // Red for high surge
        else if (surge >= 1.5) color = '#f97316' // Orange for medium surge
        else if (surge >= 1.2) color = '#eab308' // Yellow for low surge

        return {
          center: [area.lng, area.lat] as [number, number],
          radius: 2000, // 2km radius
          surge,
          color
        }
      })
    )

    return surgeAreas.filter(area => area.surge > 1.1) // Only show surge areas
  }
}

export const dynamicPricingService = new DynamicPricingService()
export default dynamicPricingService
