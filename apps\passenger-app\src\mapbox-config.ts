// Configuração global do Mapbox
const MAPBOX_TOKEN = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN

// Definir globalmente no window
if (typeof window !== 'undefined') {
  (window as any).MAPBOX_TOKEN = MAPBOX_TOKEN
}

// Importar e configurar mapboxgl
import mapboxgl from 'mapbox-gl'
mapboxgl.accessToken = MAPBOX_TOKEN

// Exportar token para uso em componentes se necessário
export const mapboxToken = MAPBOX_TOKEN
export default mapboxgl
