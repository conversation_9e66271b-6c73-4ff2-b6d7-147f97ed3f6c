-- Migration to create a view for available drivers with their details

CREATE OR REPLACE VIEW public.available_drivers_with_details AS
SELECT
    dl.user_id,
    dl.id AS location_id,
    dl.latitude,
    dl.longitude,
    dl.is_active AS driver_is_active, -- aliasing to avoid conflict if profiles also has is_active
    dl.is_available AS driver_is_available, -- aliasing
    dp.vehicle_type,
    dp.id AS profile_id,
    p.full_name AS driver_name,
    dp.rating AS driver_rating
FROM
    public.driver_locations dl
JOIN
    public.driver_profiles dp ON dl.user_id = dp.user_id
LEFT JOIN
    public.profiles p ON dl.user_id = p.id; -- Assuming 'public.profiles' table

-- Grant select access to roles that need to query this view.
-- For example, if your app uses the 'authenticated' role for queries:
GRANT SELECT ON public.available_drivers_with_details TO authenticated;
-- If anon users can see some of this (unlikely for this specific view):
-- GRANT SELECT ON public.available_drivers_with_details TO anon;

COMMENT ON VIEW public.available_drivers_with_details IS 'Provides a pre-joined view of driver locations, their profiles (including vehicle type and rating), and driver names from the general profiles table.';
