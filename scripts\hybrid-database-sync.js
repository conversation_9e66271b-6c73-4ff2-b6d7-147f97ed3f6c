#!/usr/bin/env node

/**
 * MÉTODO HÍBRIDO - Sincronização Completa do Banco MobDrive
 * 
 * Este script combina:
 * 1. Tentativa automática via API
 * 2. Geração de SQL para execução manual
 * 3. Verificação completa do resultado
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuração do Supabase (usando credenciais que já funcionam)
const supabaseUrl = 'https://udquhavmgqtpkubrfzdm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzU2NzI4NzQsImV4cCI6MjA1MTI0ODg3NH0.KrCJ_WZ_79a92hFSISIq4g';

const supabase = createClient(supabaseUrl, supabaseKey);

// Função para verificar se uma tabela existe
async function tableExists(tableName) {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('count')
      .limit(1);
    
    return !error;
  } catch (error) {
    return false;
  }
}

// Função para verificar status atual
async function checkCurrentStatus() {
  console.log('🔍 Verificando status atual do banco...');
  
  const tables = ['profiles', 'ride_requests', 'driver_locations', 'driver_profiles', 'driver_notifications', 'ride_matching_history'];
  const status = {};
  
  for (const table of tables) {
    const exists = await tableExists(table);
    status[table] = exists;
    console.log(`  ${exists ? '✅' : '❌'} ${table}: ${exists ? 'existe' : 'não existe'}`);
  }
  
  return status;
}

// Função para tentar criar tabelas via RPC
async function tryAutomaticCreation() {
  console.log('\n🤖 MÉTODO 1: Tentativa automática via API...');
  
  try {
    // Tentar criar função RPC personalizada
    const createRpcFunction = `
      CREATE OR REPLACE FUNCTION create_missing_tables()
      RETURNS text
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
        -- Criar driver_profiles se não existir
        CREATE TABLE IF NOT EXISTS driver_profiles (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          vehicle_type VARCHAR(50) DEFAULT 'economy',
          vehicle_make VARCHAR(100),
          vehicle_model VARCHAR(100),
          vehicle_year INTEGER,
          vehicle_color VARCHAR(50),
          vehicle_plate VARCHAR(20),
          license_number VARCHAR(50),
          license_expiry DATE,
          rating DECIMAL(3, 2) DEFAULT 4.5,
          total_rides INTEGER DEFAULT 0,
          is_verified BOOLEAN DEFAULT false,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(user_id)
        );
        
        RETURN 'Tables created successfully';
      EXCEPTION
        WHEN OTHERS THEN
          RETURN 'Error: ' || SQLERRM;
      END;
      $$;
    `;
    
    // Tentar executar via RPC
    const { data, error } = await supabase.rpc('create_missing_tables');
    
    if (error) {
      console.log('⚠️ Método automático não disponível:', error.message);
      return false;
    }
    
    console.log('✅ Método automático executado:', data);
    return true;
    
  } catch (error) {
    console.log('⚠️ Método automático falhou:', error.message);
    return false;
  }
}

// Função para gerar SQL completo
function generateCompleteSQL() {
  console.log('\n📝 MÉTODO 2: Gerando SQL para execução manual...');
  
  const sqlContent = `-- =====================================================
-- MOBIDRIVE - SINCRONIZAÇÃO COMPLETA DO BANCO
-- =====================================================
-- Execute este SQL no dashboard: https://supabase.com/dashboard/project/udquhavmgqtpkubrfzdm/editor
-- =====================================================

-- 1. CRIAR TABELA DRIVER_PROFILES
CREATE TABLE IF NOT EXISTS driver_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    vehicle_type VARCHAR(50) DEFAULT 'economy',
    vehicle_make VARCHAR(100),
    vehicle_model VARCHAR(100),
    vehicle_year INTEGER,
    vehicle_color VARCHAR(50),
    vehicle_plate VARCHAR(20),
    license_number VARCHAR(50),
    license_expiry DATE,
    rating DECIMAL(3, 2) DEFAULT 4.5,
    total_rides INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- 2. CRIAR TABELA DRIVER_NOTIFICATIONS
CREATE TABLE IF NOT EXISTS driver_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    driver_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    ride_id UUID REFERENCES ride_requests(id) ON DELETE CASCADE,
    notification_type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT false,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. CRIAR TABELA RIDE_MATCHING_HISTORY
CREATE TABLE IF NOT EXISTS ride_matching_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    ride_id UUID NOT NULL REFERENCES ride_requests(id) ON DELETE CASCADE,
    driver_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    matching_score DECIMAL(5, 2),
    distance_km DECIMAL(8, 3),
    eta_minutes INTEGER,
    notification_sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    response_received_at TIMESTAMP WITH TIME ZONE,
    response_type VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. CRIAR ÍNDICES
CREATE INDEX IF NOT EXISTS idx_driver_profiles_user_id ON driver_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_driver_profiles_active ON driver_profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_driver_notifications_driver_id ON driver_notifications(driver_id);
CREATE INDEX IF NOT EXISTS idx_ride_matching_history_ride_id ON ride_matching_history(ride_id);

-- 5. HABILITAR RLS
ALTER TABLE driver_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE ride_matching_history ENABLE ROW LEVEL SECURITY;

-- 6. CRIAR POLÍTICAS RLS
CREATE POLICY "Anyone can view active driver profiles" ON driver_profiles
    FOR SELECT USING (is_active = true);

CREATE POLICY "Drivers can insert their own profile" ON driver_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Drivers can update their own profile" ON driver_profiles
    FOR UPDATE USING (auth.uid() = user_id);

-- 7. CONCEDER PERMISSÕES
GRANT ALL ON driver_profiles TO authenticated;
GRANT ALL ON driver_notifications TO authenticated;
GRANT ALL ON ride_matching_history TO authenticated;

-- 8. VERIFICAÇÃO
SELECT 'driver_profiles' as table_name, COUNT(*) as exists FROM information_schema.tables WHERE table_name = 'driver_profiles'
UNION ALL
SELECT 'driver_notifications' as table_name, COUNT(*) as exists FROM information_schema.tables WHERE table_name = 'driver_notifications'
UNION ALL
SELECT 'ride_matching_history' as table_name, COUNT(*) as exists FROM information_schema.tables WHERE table_name = 'ride_matching_history';
`;

  // Salvar SQL em arquivo
  const sqlPath = path.join(__dirname, '..', 'database', 'MANUAL_IMPORT.sql');
  fs.writeFileSync(sqlPath, sqlContent);
  
  console.log(`✅ SQL gerado em: ${sqlPath}`);
  console.log('\n📋 INSTRUÇÕES PARA EXECUÇÃO MANUAL:');
  console.log('1. Acesse: https://supabase.com/dashboard/project/udquhavmgqtpkubrfzdm/editor');
  console.log('2. Clique em "SQL Editor" > "New Query"');
  console.log('3. Copie e cole o SQL do arquivo MANUAL_IMPORT.sql');
  console.log('4. Clique em "Run" para executar');
  
  return sqlPath;
}

// Função principal
async function hybridSync() {
  console.log('🚀 SINCRONIZAÇÃO HÍBRIDA DO BANCO MOBIDRIVE');
  console.log('=' .repeat(50));
  
  try {
    // Verificar conexão
    const { data, error } = await supabase.from('profiles').select('count').limit(1);
    if (error) {
      throw new Error(`Erro de conexão: ${error.message}`);
    }
    console.log('✅ Conexão com Supabase estabelecida');
    
    // Verificar status atual
    const initialStatus = await checkCurrentStatus();
    const missingTables = Object.entries(initialStatus)
      .filter(([table, exists]) => !exists)
      .map(([table]) => table);
    
    if (missingTables.length === 0) {
      console.log('\n🎉 Todas as tabelas já existem! Banco sincronizado.');
      return;
    }
    
    console.log(`\n⚠️ Tabelas faltantes: ${missingTables.join(', ')}`);
    
    // MÉTODO 1: Tentativa automática
    const automaticSuccess = await tryAutomaticCreation();
    
    // MÉTODO 2: Gerar SQL para execução manual
    const sqlPath = generateCompleteSQL();
    
    // Aguardar um pouco e verificar novamente
    console.log('\n⏳ Aguardando 3 segundos para verificar resultado...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Verificação final
    console.log('\n🔍 Verificação final...');
    const finalStatus = await checkCurrentStatus();
    
    const stillMissing = Object.entries(finalStatus)
      .filter(([table, exists]) => !exists)
      .map(([table]) => table);
    
    if (stillMissing.length === 0) {
      console.log('\n🎉 SUCESSO COMPLETO! Todas as tabelas foram criadas.');
      console.log('✅ Banco de dados totalmente sincronizado.');
    } else {
      console.log(`\n⚠️ Ainda faltam: ${stillMissing.join(', ')}`);
      console.log('\n📝 PRÓXIMOS PASSOS:');
      console.log('1. Execute o SQL manual gerado em:', sqlPath);
      console.log('2. Ou execute: npm run test:connection para verificar novamente');
    }
    
    // Executar teste de conexão final
    console.log('\n🧪 Executando teste de conexão...');
    
  } catch (error) {
    console.error('❌ Erro na sincronização:', error.message);
    
    // Mesmo com erro, gerar SQL manual
    console.log('\n🔧 Gerando SQL manual como fallback...');
    generateCompleteSQL();
  }
}

// Executar
hybridSync().catch(console.error);
