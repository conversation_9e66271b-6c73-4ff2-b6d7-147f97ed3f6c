#!/usr/bin/env node

/**
 * Monitor simples e eficiente para Supabase
 * Substitui os scripts complexos anteriores
 */

import { createClient } from '@supabase/supabase-js'
import express from 'express'
import cors from 'cors'
import dotenv from 'dotenv'

dotenv.config()

class SimpleSupabaseMonitor {
  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.VITE_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY
    )
    
    this.app = express()
    this.port = process.env.MONITOR_PORT || 3001
    this.isConnected = false
    this.lastCheck = null
    this.stats = {
      totalChecks: 0,
      successfulChecks: 0,
      errors: []
    }
    
    this.setupMiddleware()
    this.setupRoutes()
  }

  setupMiddleware() {
    this.app.use(cors())
    this.app.use(express.json())
    this.app.use(express.static('public'))
  }

  setupRoutes() {
    // Status de saúde
    this.app.get('/api/health', async (req, res) => {
      const health = await this.checkHealth()
      res.json(health)
    })

    // Teste de conexão
    this.app.post('/api/test', async (req, res) => {
      const result = await this.testConnection()
      res.json(result)
    })

    // Estatísticas
    this.app.get('/api/stats', (req, res) => {
      res.json({
        ...this.stats,
        uptime: process.uptime(),
        lastCheck: this.lastCheck,
        isConnected: this.isConnected
      })
    })

    // Dashboard simples
    this.app.get('/', (req, res) => {
      res.send(this.getDashboardHTML())
    })
  }

  async checkHealth() {
    this.stats.totalChecks++
    
    try {
      const start = Date.now()
      
      // Teste básico de conexão
      const { data, error } = await this.supabase
        .from('ride_requests')
        .select('count')
        .limit(1)

      const latency = Date.now() - start

      if (error) throw error

      this.isConnected = true
      this.stats.successfulChecks++
      this.lastCheck = new Date().toISOString()

      return {
        status: 'healthy',
        connected: true,
        latency,
        timestamp: this.lastCheck,
        message: 'Conexão OK'
      }
    } catch (error) {
      this.isConnected = false
      this.stats.errors.push({
        timestamp: new Date().toISOString(),
        message: error.message
      })

      // Manter apenas os últimos 10 erros
      if (this.stats.errors.length > 10) {
        this.stats.errors = this.stats.errors.slice(-10)
      }

      return {
        status: 'unhealthy',
        connected: false,
        timestamp: new Date().toISOString(),
        error: error.message
      }
    }
  }

  async testConnection() {
    console.log('🔄 Testando conexão...')
    
    try {
      // Teste de tabelas principais
      const tables = ['ride_requests', 'driver_locations', 'profiles']
      const results = {}

      for (const table of tables) {
        try {
          const { data, error } = await this.supabase
            .from(table)
            .select('*')
            .limit(1)
          
          results[table] = {
            accessible: !error,
            error: error?.message,
            records: data?.length || 0
          }
        } catch (err) {
          results[table] = {
            accessible: false,
            error: err.message
          }
        }
      }

      // Teste de autenticação
      const { data: { user }, error: authError } = await this.supabase.auth.getUser()
      
      return {
        success: true,
        tables: results,
        auth: {
          working: !authError || authError.message === 'Auth session missing!',
          user: user?.email || null,
          error: authError?.message
        },
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  getDashboardHTML() {
    return `
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>MobDrive - Monitor Supabase</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { font-family: 'Segoe UI', sans-serif; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; padding: 20px; }
            .header { background: #1a1a1a; color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
            .card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
            .status { display: flex; align-items: center; gap: 10px; margin-bottom: 15px; }
            .status-dot { width: 12px; height: 12px; border-radius: 50%; }
            .status-dot.online { background: #4CAF50; }
            .status-dot.offline { background: #f44336; }
            .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
            .btn-primary { background: #2196F3; color: white; }
            .btn:hover { opacity: 0.8; }
            .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
            .stats { display: flex; justify-content: space-between; }
            .stat { text-align: center; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚗 MobDrive - Monitor Supabase</h1>
                <p>Monitor simplificado e eficiente</p>
            </div>
            
            <div class="grid">
                <div class="card">
                    <h3>Status da Conexão</h3>
                    <div class="status">
                        <div class="status-dot" id="statusDot"></div>
                        <span id="statusText">Verificando...</span>
                    </div>
                    <button class="btn btn-primary" onclick="testConnection()">Testar Conexão</button>
                </div>
                
                <div class="card">
                    <h3>Estatísticas</h3>
                    <div class="stats">
                        <div class="stat">
                            <div id="totalChecks">-</div>
                            <small>Total de Checks</small>
                        </div>
                        <div class="stat">
                            <div id="successRate">-</div>
                            <small>Taxa de Sucesso</small>
                        </div>
                        <div class="stat">
                            <div id="uptime">-</div>
                            <small>Uptime</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>Último Check</h3>
                <p id="lastCheck">-</p>
            </div>
        </div>

        <script>
            async function checkHealth() {
                try {
                    const response = await fetch('/api/health');
                    const data = await response.json();
                    updateStatus(data.connected, data.message || data.error);
                } catch (error) {
                    updateStatus(false, 'Erro na requisição');
                }
            }
            
            async function testConnection() {
                try {
                    const response = await fetch('/api/test', { method: 'POST' });
                    const data = await response.json();
                    console.log('Resultado do teste:', data);
                    alert(data.success ? 'Teste realizado com sucesso!' : 'Erro no teste: ' + data.error);
                } catch (error) {
                    alert('Erro: ' + error.message);
                }
            }
            
            async function loadStats() {
                try {
                    const response = await fetch('/api/stats');
                    const data = await response.json();
                    
                    document.getElementById('totalChecks').textContent = data.totalChecks;
                    document.getElementById('successRate').textContent = 
                        data.totalChecks > 0 ? Math.round((data.successfulChecks / data.totalChecks) * 100) + '%' : '0%';
                    document.getElementById('uptime').textContent = Math.round(data.uptime) + 's';
                    document.getElementById('lastCheck').textContent = data.lastCheck || 'Nunca';
                } catch (error) {
                    console.error('Erro ao carregar estatísticas:', error);
                }
            }
            
            function updateStatus(connected, message) {
                const dot = document.getElementById('statusDot');
                const text = document.getElementById('statusText');
                
                if (connected) {
                    dot.className = 'status-dot online';
                    text.textContent = 'Conectado - ' + message;
                } else {
                    dot.className = 'status-dot offline';
                    text.textContent = 'Desconectado - ' + message;
                }
            }
            
            // Verificar status a cada 30 segundos
            setInterval(() => {
                checkHealth();
                loadStats();
            }, 30000);
            
            // Verificação inicial
            checkHealth();
            loadStats();
        </script>
    </body>
    </html>
    `;
  }

  async start() {
    // Verificação inicial
    await this.checkHealth()
    
    this.app.listen(this.port, () => {
      console.log(`🖥️ Monitor Supabase rodando em http://localhost:${this.port}`)
      console.log('📊 Dashboard disponível no navegador')
    })

    // Verificação periódica a cada 5 minutos
    setInterval(() => {
      this.checkHealth()
    }, 5 * 60 * 1000)
  }
}

// Iniciar se executado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const monitor = new SimpleSupabaseMonitor()
  monitor.start().catch(console.error)
}

export default SimpleSupabaseMonitor
