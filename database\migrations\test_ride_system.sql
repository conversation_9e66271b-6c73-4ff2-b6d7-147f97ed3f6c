-- <PERSON>ript de teste para o sistema de corridas

-- 1. Limpar dados de teste anteriores
DO $$ 
BEGIN
    DELETE FROM ride_requests WHERE is_test = true;
END $$;

-- 2. <PERSON><PERSON>r função de teste
CREATE OR REPLACE FUNCTION test_ride_system()
RETURNS TEXT AS $$
DECLARE
    test_user_id UUID;
    test_driver_id UUID;
    test_ride_id UUID;
    current_status TEXT;
    test_result TEXT;
BEGIN
    -- <PERSON><PERSON>r usu<PERSON><PERSON> de teste (se não existir)
    INSERT INTO auth.users (email, encrypted_password, email_confirmed_at)
    VALUES ('<EMAIL>', 'test123', NOW())
    ON CONFLICT (email) DO NOTHING
    RETURNING id INTO test_user_id;

    -- Criar motorista de teste (se não existir)
    INSERT INTO auth.users (email, encrypted_password, email_confirmed_at)
    VALUES ('<EMAIL>', 'test123', NOW())
    ON CONFLICT (email) DO NOTHING
    RETURNING id INTO test_driver_id;

    -- 1. Teste: Criar nova corrida
    INSERT INTO ride_requests (
        user_id,
        pickup_latitude,
        pickup_longitude,
        pickup_address,
        destination_latitude,
        destination_longitude,
        destination_address,
        vehicle_type,
        payment_method,
        status,
        is_test
    ) VALUES (
        test_user_id,
        -12.1789,
        -44.9891,
        'Rua Teste, 123',
        -12.1901,
        -44.9923,
        'Avenida Teste, 456',
        'economy',
        'cash',
        'pending',
        true
    ) RETURNING id INTO test_ride_id;

    -- 2. Teste: Atualizar para searching
    UPDATE ride_requests 
    SET status = 'searching'
    WHERE id = test_ride_id;

    -- 3. Teste: Motorista aceita corrida
    UPDATE ride_requests 
    SET 
        status = 'accepted',
        driver_id = test_driver_id,
        accepted_at = NOW()
    WHERE id = test_ride_id;

    -- 4. Teste: Motorista chega ao local
    UPDATE ride_requests 
    SET status = 'arrived'
    WHERE id = test_ride_id;

    -- 5. Teste: Iniciar corrida
    UPDATE ride_requests 
    SET 
        status = 'in_progress',
        started_at = NOW()
    WHERE id = test_ride_id;

    -- 6. Teste: Finalizar corrida
    UPDATE ride_requests 
    SET 
        status = 'completed',
        completed_at = NOW(),
        final_price = 25.50
    WHERE id = test_ride_id;

    -- Verificar se todos os estados foram atualizados corretamente
    SELECT status INTO current_status 
    FROM ride_requests 
    WHERE id = test_ride_id;

    IF current_status = 'completed' THEN
        test_result := 'Todos os testes passaram com sucesso!';
    ELSE
        test_result := 'Erro nos testes: status final incorreto';
    END IF;

    -- Teste de validações
    BEGIN
        -- Tentar inserir coordenadas inválidas
        INSERT INTO ride_requests (
            user_id,
            pickup_latitude,
            pickup_longitude,
            pickup_address,
            destination_latitude,
            destination_longitude,
            destination_address,
            is_test
        ) VALUES (
            test_user_id,
            91, -- latitude inválida
            -44.9891,
            'Teste',
            -12.1901,
            -44.9923,
            'Teste',
            true
        );
        test_result := test_result || ' ERRO: Validação de coordenadas falhou';
    EXCEPTION WHEN OTHERS THEN
        test_result := test_result || ' Validação de coordenadas OK';
    END;

    -- Testar políticas de segurança
    BEGIN
        -- Tentar atualizar corrida com usuário errado
        UPDATE ride_requests 
        SET status = 'cancelled'
        WHERE id = test_ride_id 
        AND user_id = 'invalid-uuid';
        
        test_result := test_result || ' ERRO: Política de segurança falhou';
    EXCEPTION WHEN OTHERS THEN
        test_result := test_result || ' Política de segurança OK';
    END;

    RETURN test_result;
END;
$$ LANGUAGE plpgsql;

-- 3. Executar os testes
SELECT test_ride_system();

-- 4. Limpar dados de teste
DO $$ 
BEGIN
    DELETE FROM ride_requests WHERE is_test = true;
END $$;