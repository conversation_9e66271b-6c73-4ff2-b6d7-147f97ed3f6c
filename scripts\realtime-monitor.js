#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js'
import chalk from 'chalk'

const SUPABASE_URL = process.env.SUPABASE_URL || 'https://udquhavmgqtpkubrfzdm.supabase.co'
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

class RealtimeMonitor {
  constructor() {
    this.subscriptions = new Map()
    this.stats = {
      connections: 0,
      messages: 0,
      errors: 0,
      startTime: Date.now()
    }
  }

  start() {
    console.log(chalk.blue('🚀 Iniciando monitor em tempo real...'))
    console.log(chalk.gray(`📡 Conectando em: ${SUPABASE_URL}`))

    this.setupSubscriptions()
    this.startStatsDisplay()

    // Graceful shutdown
    process.on('SIGINT', () => {
      console.log(chalk.yellow('\n🛑 Encerrando monitor...'))
      this.cleanup()
      process.exit(0)
    })
  }

  setupSubscriptions() {
    // Monitor ride_requests
    this.subscribeToTable('ride_requests', {
      event: '*',
      callback: (payload) => this.handleRideUpdate(payload)
    })

    // Monitor driver_locations
    this.subscribeToTable('driver_locations', {
      event: '*',
      callback: (payload) => this.handleLocationUpdate(payload)
    })

    // Monitor profiles
    this.subscribeToTable('profiles', {
      event: '*',
      callback: (payload) => this.handleProfileUpdate(payload)
    })
  }

  subscribeToTable(table, { event, callback }) {
    console.log(chalk.green(`📡 Inscrevendo-se na tabela: ${table}`))

    const subscription = supabase
      .channel(`${table}_changes`)
      .on('postgres_changes',
        {
          event,
          schema: 'public',
          table
        },
        (payload) => {
          this.stats.messages++
          callback(payload)
        }
      )
      .on('subscribe', (status) => {
        if (status === 'SUBSCRIBED') {
          this.stats.connections++
          console.log(chalk.green(`✅ Conectado à tabela: ${table}`))
        }
      })
      .on('error', (error) => {
        this.stats.errors++
        console.error(chalk.red(`❌ Erro na tabela ${table}:`), error)
      })
      .subscribe()

    this.subscriptions.set(table, subscription)
  }

  handleRideUpdate(payload) {
    const { eventType, new: newRecord, old: oldRecord } = payload

    console.log(chalk.cyan('\n🚗 ATUALIZAÇÃO DE CORRIDA'))
    console.log(chalk.gray(`Evento: ${eventType}`))

    if (eventType === 'INSERT') {
      console.log(chalk.green(`➕ Nova corrida criada: ${newRecord.id}`))
      console.log(`   Status: ${newRecord.status}`)
      console.log(`   Origem: ${newRecord.pickup_address}`)
      console.log(`   Destino: ${newRecord.destination_address}`)
    }

    if (eventType === 'UPDATE') {
      console.log(chalk.yellow(`🔄 Corrida atualizada: ${newRecord.id}`))
      if (oldRecord.status !== newRecord.status) {
        console.log(`   Status: ${oldRecord.status} → ${newRecord.status}`)
      }
      if (newRecord.driver_id && !oldRecord.driver_id) {
        console.log(`   Motorista aceito: ${newRecord.driver_id}`)
      }
    }

    if (eventType === 'DELETE') {
      console.log(chalk.red(`➖ Corrida removida: ${oldRecord.id}`))
    }
  }

  handleLocationUpdate(payload) {
    const { eventType, new: newRecord } = payload

    if (eventType === 'UPDATE' || eventType === 'INSERT') {
      console.log(chalk.magenta('\n📍 LOCALIZAÇÃO ATUALIZADA'))
      console.log(`   Motorista: ${newRecord.driver_id}`)
      console.log(`   Coordenadas: ${newRecord.latitude}, ${newRecord.longitude}`)
      console.log(`   Disponível: ${newRecord.is_available ? 'Sim' : 'Não'}`)
    }
  }

  handleProfileUpdate(payload) {
    const { eventType, new: newRecord } = payload

    console.log(chalk.blue('\n👤 PERFIL ATUALIZADO'))
    console.log(chalk.gray(`Evento: ${eventType}`))
    console.log(`   Usuário: ${newRecord.id}`)
    console.log(`   Nome: ${newRecord.full_name || 'N/A'}`)
  }

  startStatsDisplay() {
    setInterval(() => {
      this.displayStats()
    }, 30000) // A cada 30 segundos
  }

  displayStats() {
    const uptime = Math.floor((Date.now() - this.stats.startTime) / 1000)

    console.log(chalk.blue('\n📊 ESTATÍSTICAS DO MONITOR'))
    console.log(chalk.gray('─'.repeat(40)))
    console.log(`⏱️  Tempo ativo: ${uptime}s`)
    console.log(`🔗 Conexões: ${this.stats.connections}`)
    console.log(`📨 Mensagens: ${this.stats.messages}`)
    console.log(`❌ Erros: ${this.stats.errors}`)
    console.log(chalk.gray('─'.repeat(40)))
  }

  cleanup() {
    console.log(chalk.yellow('🧹 Limpando subscriptions...'))

    for (const [table, subscription] of this.subscriptions) {
      subscription.unsubscribe()
      console.log(chalk.gray(`   Desconectado de: ${table}`))
    }

    this.subscriptions.clear()
    console.log(chalk.green('✅ Cleanup concluído'))
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const monitor = new RealtimeMonitor()
  monitor.start()
}

export default RealtimeMonitor