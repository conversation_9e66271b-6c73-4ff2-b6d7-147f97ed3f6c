import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { supabase } from './lib/supabase'

// ⚡ CONFIGURAÇÃO OTIMIZADA - COMUNICAÇÃO SUPABASE ROBUSTA
// Configure Mapbox token immediately (required for map components)
const MAPBOX_TOKEN = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g'

if (typeof window !== 'undefined') {
  (window as any).MAPBOX_ACCESS_TOKEN = MAPBOX_TOKEN
}

// Configuração para comunicação robusta com Supabase
const setupRobustSupabaseConnection = () => {
  // Verificar conexão inicial
  const checkInitialConnection = async () => {
    try {
      const { data, error } = await supabase.from('app_settings').select('count').limit(1)
      console.log(`✅ Conexão inicial com Supabase: ${error ? 'falha' : 'sucesso'}`)
    } catch (e) {
      console.warn('⚠️ Erro na verificação inicial:', e)
    }
  }

  // Configurar reconexão automática
  if (typeof window !== 'undefined') {
    // Monitorar eventos de conexão
    window.addEventListener('online', async () => {
      console.log('🌐 Conexão de internet restaurada. Reconectando Supabase...')
      try {
        if (supabase.realtime && !supabase.realtime.isConnected()) {
          await supabase.realtime.connect()
          console.log('✅ Conexão Realtime restabelecida')
        }
      } catch (e) {
        console.error('❌ Erro ao reconectar Realtime:', e)
      }
    })

    window.addEventListener('offline', () => {
      console.log('🔌 Conexão de internet perdida. Supabase entrando em modo offline...')
    })

    // Interceptar erros relacionados ao Supabase
    window.addEventListener('error', (event) => {
      if (event.message?.includes('supabase') || event.filename?.includes('supabase')) {
        console.error('❌ Erro relacionado ao Supabase detectado:', event)
        // Tentar reconectar após erro
        if (supabase.realtime && navigator.onLine) {
          setTimeout(() => supabase.realtime.connect(), 2000)
        }
      }
    })
  }

  // Verificar conexão inicial
  checkInitialConnection()

  // Verificar periodicamente a conexão
  setInterval(async () => {
    if (navigator.onLine) {
      try {
        const { error } = await supabase.from('app_settings').select('count').limit(1)
        if (error) {
          console.warn('⚠️ Verificação periódica falhou:', error.message)
          // Tentar reconectar
          if (supabase.realtime) {
            supabase.realtime.connect()
          }
        }
      } catch (e) {
        console.warn('⚠️ Erro na verificação periódica:', e)
      }
    }
  }, 30000) // A cada 30 segundos
}

// Render app immediately
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)

// Inicializar serviços após renderização
const initializeServices = async () => {
  try {
    // Configurar conexão robusta com Supabase
    setupRobustSupabaseConnection()

    // Importar serviços específicos do app
    const { analyticsService } = await import('./services/AnalyticsService')
    const { cacheService } = await import('./services/CacheService')

    // Inicializar serviços com um pequeno atraso para não bloquear a UI
    setTimeout(() => {
      analyticsService.trackPageView('app_start', performance.now())
      cacheService.preloadCommonData()
    }, 100)

    // Carregar utilitários de teste apenas em desenvolvimento
    if (import.meta.env.DEV) {
      import('./utils/testRealtime')
    }

    console.log('✅ Serviços do app inicializados com sucesso')

  } catch (error) {
    console.warn('⚠️ Falha na inicialização dos serviços:', error)
  }
}
// Inicializar serviços
initializeServices()