/**
 * Sistema de monitoramento de saúde da conexão com o Supabase
 * Monitora a conexão e notifica quando há problemas
 */

import { supabase, supabaseHelpers } from './supabase-client';
import { supabaseErrorHandler, SupabaseErrorType } from './supabase-error-handler';

// Status de saúde
export enum HealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
  OFFLINE = 'offline',
  UNKNOWN = 'unknown'
}

// Interface para verificação de saúde
export interface HealthCheck {
  status: HealthStatus;
  latency: number;
  timestamp: number;
  message: string;
  details?: any;
}

// Interface para listeners
export interface HealthListener {
  id: string;
  callback: (check: HealthCheck) => void;
}

/**
 * Classe para monitorar a saúde da conexão com o Supabase
 */
class SupabaseHealthMonitor {
  private static instance: SupabaseHealthMonitor;
  private checkInterval: any = null;
  private listeners: HealthListener[] = [];
  private history: HealthCheck[] = [];
  private maxHistorySize = 20;
  private lastCheck: HealthCheck | null = null;
  private checkIntervalTime = 30000; // 30 segundos
  private consecutiveFailures = 0;
  private maxConsecutiveFailures = 3;
  private isCheckingHealth = false;
  
  // Status de limites para cada nível
  private thresholds = {
    latency: {
      healthy: 500,  // ms
      degraded: 1500 // ms
    }
  };
  
  // Singleton pattern
  public static getInstance(): SupabaseHealthMonitor {
    if (!SupabaseHealthMonitor.instance) {
      SupabaseHealthMonitor.instance = new SupabaseHealthMonitor();
    }
    return SupabaseHealthMonitor.instance;
  }
  
  private constructor() {
    this.setupConnectionMonitoring();
    this.startCheckInterval();
    
    // Verificar saúde inicial
    if (typeof window !== 'undefined') {
      setTimeout(() => {
        this.checkHealth();
      }, 2000);
    }
  }
  
  /**
   * Configurar monitoramento de conexão
   */
  private setupConnectionMonitoring(): void {
    if (typeof window === 'undefined') return;
    
    // Monitorar eventos de conexão
    window.addEventListener('online', () => {
      this.addHealthCheck({
        status: HealthStatus.UNKNOWN,
        latency: 0,
        timestamp: Date.now(),
        message: 'Conexão de rede restaurada, verificando Supabase...'
      });
      
      // Verificar saúde imediatamente após reconexão
      this.checkHealth();
    });
    
    window.addEventListener('offline', () => {
      const check: HealthCheck = {
        status: HealthStatus.OFFLINE,
        latency: 0,
        timestamp: Date.now(),
        message: 'Dispositivo offline'
      };
      
      this.lastCheck = check;
      this.addHealthCheck(check);
      this.notifyListeners(check);
    });
    
    // Monitorar erros relacionados ao Supabase
    supabaseErrorHandler.addListener((error) => {
      if (error.type === SupabaseErrorType.NETWORK || error.type === SupabaseErrorType.DATABASE) {
        // Verificar saúde após erro de rede ou banco
        this.checkHealth();
      }
    });
  }
  
  /**
   * Iniciar intervalo de verificação
   */
  private startCheckInterval(): void {
    if (typeof window === 'undefined') return;
    
    // Limpar intervalo existente
    this.stopCheckInterval();
    
    // Criar novo intervalo
    this.checkInterval = setInterval(() => {
      this.checkHealth();
    }, this.checkIntervalTime);
    
    console.log(`⏱️ Monitoramento de saúde iniciado (verificando a cada ${this.checkIntervalTime / 1000}s)`);
  }
  
  /**
   * Parar intervalo de verificação
   */
  private stopCheckInterval(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }
  
  /**
   * Verificar saúde da conexão
   */
  public async checkHealth(): Promise<HealthCheck> {
    // Evitar verificações simultâneas
    if (this.isCheckingHealth) {
      return this.lastCheck || {
        status: HealthStatus.UNKNOWN,
        latency: 0,
        timestamp: Date.now(),
        message: 'Verificação em andamento'
      };
    }
    
    // Verificar se está offline
    if (typeof navigator !== 'undefined' && !navigator.onLine) {
      const check: HealthCheck = {
        status: HealthStatus.OFFLINE,
        latency: 0,
        timestamp: Date.now(),
        message: 'Dispositivo offline'
      };
      
      this.lastCheck = check;
      this.addHealthCheck(check);
      this.notifyListeners(check);
      
      return check;
    }
    
    this.isCheckingHealth = true;
    const startTime = Date.now();
    
    try {
      // Verificar conexão com Supabase
      const { connected, latency } = await supabaseHelpers.checkConnection();
      
      // Determinar status com base na latência e conectividade
      let status: HealthStatus;
      let message: string;
      
      if (!connected) {
        status = HealthStatus.UNHEALTHY;
        message = 'Não foi possível conectar ao Supabase';
        this.consecutiveFailures++;
      } else if (latency > this.thresholds.latency.degraded) {
        status = HealthStatus.DEGRADED;
        message = `Conexão lenta (${latency}ms)`;
        // Resetar contagem de falhas consecutivas
        this.consecutiveFailures = 0;
      } else if (latency > this.thresholds.latency.healthy) {
        status = HealthStatus.DEGRADED;
        message = `Conexão aceitável (${latency}ms)`;
        // Resetar contagem de falhas consecutivas
        this.consecutiveFailures = 0;
      } else {
        status = HealthStatus.HEALTHY;
        message = `Conexão saudável (${latency}ms)`;
        // Resetar contagem de falhas consecutivas
        this.consecutiveFailures = 0;
      }
      
      // Verificar status do Realtime
      let realtimeConnected = false;
      try {
        realtimeConnected = supabase.realtime && supabase.realtime.isConnected();
      } catch (e) {
        console.warn('⚠️ Erro ao verificar status do Realtime:', e);
      }
      
      // Detalhes adicionais
      const details = {
        realtimeConnected,
        consecutiveFailures: this.consecutiveFailures,
        authSession: !!(await supabase.auth.getSession()).data.session
      };
      
      // Tentativa de reconexão se houver muitas falhas consecutivas
      if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
        console.warn(`⚠️ ${this.consecutiveFailures} falhas consecutivas detectadas, tentando reconectar...`);
        
        try {
          // Tentar reconectar ao Realtime
          if (supabase.realtime) {
            await supabase.realtime.connect();
          }
          
          // Verificar novamente após reconexão
          const secondCheck = await supabaseHelpers.checkConnection();
          if (secondCheck.connected) {
            status = HealthStatus.DEGRADED;
            message = 'Conexão restaurada após falhas';
            this.consecutiveFailures = 0;
          }
        } catch (reconnectError) {
          console.error('❌ Falha ao reconectar:', reconnectError);
          // Não alterar status, pois a reconexão falhou
        }
      }
      
      // Criar objeto de verificação
      const check: HealthCheck = {
        status,
        latency,
        timestamp: Date.now(),
        message,
        details
      };
      
      // Atualizar último check
      this.lastCheck = check;
      
      // Adicionar ao histórico
      this.addHealthCheck(check);
      
      // Notificar listeners
      this.notifyListeners(check);
      
      return check;
    } catch (error) {
      console.error('❌ Erro ao verificar saúde:', error);
      
      // Incrementar contagem de falhas
      this.consecutiveFailures++;
      
      // Criar objeto de verificação com erro
      const check: HealthCheck = {
        status: HealthStatus.UNHEALTHY,
        latency: Date.now() - startTime,
        timestamp: Date.now(),
        message: `Erro ao verificar saúde: ${error.message}`,
        details: { error }
      };
      
      // Atualizar último check
      this.lastCheck = check;
      
      // Adicionar ao histórico
      this.addHealthCheck(check);
      
      // Notificar listeners
      this.notifyListeners(check);
      
      return check;
    } finally {
      this.isCheckingHealth = false;
    }
  }
  
  /**
   * Adicionar verificação ao histórico
   */
  private addHealthCheck(check: HealthCheck): void {
    // Adicionar ao início para manter ordem cronológica inversa
    this.history.unshift(check);
    
    // Limitar tamanho do histórico
    if (this.history.length > this.maxHistorySize) {
      this.history = this.history.slice(0, this.maxHistorySize);
    }
  }
  
  /**
   * Notificar listeners sobre nova verificação
   */
  private notifyListeners(check: HealthCheck): void {
    this.listeners.forEach(listener => {
      try {
        listener.callback(check);
      } catch (error) {
        console.error(`❌ Erro no listener de saúde ${listener.id}:`, error);
      }
    });
  }
  
  /**
   * Adicionar listener para verificações de saúde
   */
  public addListener(callback: (check: HealthCheck) => void): string {
    const id = `listener_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    this.listeners.push({ id, callback });
    
    // Notificar imediatamente com o último check
    if (this.lastCheck) {
      try {
        callback(this.lastCheck);
      } catch (error) {
        console.error(`❌ Erro no listener de saúde ${id}:`, error);
      }
    }
    
    // Retornar ID para remoção posterior
    return id;
  }
  
  /**
   * Remover listener
   */
  public removeListener(id: string): boolean {
    const initialLength = this.listeners.length;
    this.listeners = this.listeners.filter(listener => listener.id !== id);
    
    return this.listeners.length < initialLength;
  }
  
  /**
   * Obter último status de saúde
   */
  public getLastCheck(): HealthCheck | null {
    return this.lastCheck;
  }
  
  /**
   * Obter histórico de verificações
   */
  public getHistory(): HealthCheck[] {
    return [...this.history];
  }
  
  /**
   * Obter estatísticas de saúde
   */
  public getStats(): {
    successRate: number;
    averageLatency: number;
    healthyChecks: number;
    degradedChecks: number;
    unhealthyChecks: number;
    totalChecks: number;
  } {
    // Filtrar verificações sem latência (offline)
    const checksWithLatency = this.history.filter(check => check.latency > 0);
    
    // Calcular estatísticas
    const totalChecks = this.history.length;
    const healthyChecks = this.history.filter(check => check.status === HealthStatus.HEALTHY).length;
    const degradedChecks = this.history.filter(check => check.status === HealthStatus.DEGRADED).length;
    const unhealthyChecks = this.history.filter(check => check.status === HealthStatus.UNHEALTHY).length;
    
    // Calcular taxa de sucesso
    const successRate = totalChecks > 0
      ? ((healthyChecks + degradedChecks) / totalChecks) * 100
      : 0;
    
    // Calcular latência média
    const averageLatency = checksWithLatency.length > 0
      ? checksWithLatency.reduce((sum, check) => sum + check.latency, 0) / checksWithLatency.length
      : 0;
    
    return {
      successRate,
      averageLatency,
      healthyChecks,
      degradedChecks,
      unhealthyChecks,
      totalChecks
    };
  }
  
  /**
   * Verificar se a conexão está saudável
   */
  public isHealthy(): boolean {
    return this.lastCheck?.status === HealthStatus.HEALTHY;
  }
  
  /**
   * Verificar se a conexão está degradada
   */
  public isDegraded(): boolean {
    return this.lastCheck?.status === HealthStatus.DEGRADED;
  }
  
  /**
   * Verificar se a conexão está não saudável
   */
  public isUnhealthy(): boolean {
    return this.lastCheck?.status === HealthStatus.UNHEALTHY;
  }
  
  /**
   * Verificar se está offline
   */
  public isOffline(): boolean {
    return this.lastCheck?.status === HealthStatus.OFFLINE;
  }
  
  /**
   * Parar monitoramento
   */
  public stop(): void {
    this.stopCheckInterval();
    this.listeners = [];
    console.log('🛑 Monitoramento de saúde parado');
  }
  
  /**
   * Retomar monitoramento
   */
  public resume(): void {
    this.startCheckInterval();
    console.log('▶️ Monitoramento de saúde retomado');
  }
  
  /**
   * Ajustar intervalo de verificação
   */
  public setCheckInterval(interval: number): void {
    this.checkIntervalTime = interval;
    this.startCheckInterval(); // Reinicia com novo intervalo
  }
}

// Exportar instância única
export const healthMonitor = SupabaseHealthMonitor.getInstance();

// Exportar também hook para React
export function useSupabaseHealth() {
  const [healthCheck, setHealthCheck] = React.useState<HealthCheck | null>(
    healthMonitor.getLastCheck()
  );
  
  React.useEffect(() => {
    const listenerId = healthMonitor.addListener((check) => {
      setHealthCheck(check);
    });
    
    return () => {
      healthMonitor.removeListener(listenerId);
    };
  }, []);
  
  return {
    healthCheck,
    history: healthMonitor.getHistory(),
    stats: healthMonitor.getStats(),
    isHealthy: healthMonitor.isHealthy(),
    isDegraded: healthMonitor.isDegraded(),
    isUnhealthy: healthMonitor.isUnhealthy(),
    isOffline: healthMonitor.isOffline(),
    checkNow: () => healthMonitor.checkHealth()
  };
}

export default healthMonitor;