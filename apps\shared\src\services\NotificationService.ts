/**
 * Serviço de Notificação Compartilhado para MobiDrive
 * 
 * Este serviço gerencia notificações em todos os aplicativos MobiDrive,
 * incluindo notificações push, toasts e alertas.
 */

import audioService, { NotificationSoundType } from './AudioService';

export type NotificationType = 'info' | 'success' | 'warning' | 'error' | 'ride' | 'payment' | 'chat' | 'system';

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: NotificationType;
  is_read: boolean;
  created_at: string;
  read_at?: string;
  metadata?: Record<string, any>;
}

export interface NotificationOptions {
  title: string;
  message: string;
  type?: NotificationType;
  sound?: NotificationSoundType;
  autoClose?: boolean;
  duration?: number;
  onClick?: () => void;
  onClose?: () => void;
  metadata?: Record<string, any>;
}

interface NotificationServiceOptions {
  debug?: boolean;
  defaultDuration?: number;
  enableSounds?: boolean;
  soundsEnabled?: boolean;
}

// Mapeamento de tipos de notificação para sons
const DEFAULT_SOUND_MAPPING: Record<NotificationType, NotificationSoundType> = {
  info: 'default',
  success: 'success',
  warning: 'warning',
  error: 'error',
  ride: 'urgent',
  payment: 'payment',
  chat: 'message',
  system: 'warning'
};

class NotificationService {
  private static instance: NotificationService;
  private debug: boolean = false;
  private defaultDuration: number = 5000;
  private enableSounds: boolean = true;
  private soundsEnabled: boolean = true;
  private toastFunction: ((options: any) => void) | null = null;
  private supabaseClient: any = null;

  private constructor(options?: NotificationServiceOptions) {
    // Aplicar opções
    if (options) {
      this.debug = options.debug || false;
      this.defaultDuration = options.defaultDuration || 5000;
      this.enableSounds = options.enableSounds !== undefined ? options.enableSounds : true;
      this.soundsEnabled = options.soundsEnabled !== undefined ? options.soundsEnabled : true;
    }

    // Carregar configurações salvas
    this.loadSettings();
  }

  private log(level: 'debug' | 'info' | 'warn' | 'error', message: string, data?: any): void {
    if (!this.debug && level === 'debug') return;

    const prefix = '[NotificationService]';
    
    switch (level) {
      case 'debug':
        console.debug(`${prefix} ${message}`, data);
        break;
      case 'info':
        console.info(`${prefix} ${message}`, data);
        break;
      case 'warn':
        console.warn(`${prefix} ${message}`, data);
        break;
      case 'error':
        console.error(`${prefix} ${message}`, data);
        break;
    }
  }

  private loadSettings(): void {
    try {
      const savedSoundsEnabled = localStorage.getItem('mobdrive-notificationSoundsEnabled');
      
      if (savedSoundsEnabled) {
        this.soundsEnabled = savedSoundsEnabled === 'true';
      }
    } catch (error) {
      this.log('error', 'Erro ao carregar configurações de notificação', error);
    }
  }

  private saveSettings(): void {
    try {
      localStorage.setItem('mobdrive-notificationSoundsEnabled', this.soundsEnabled.toString());
    } catch (error) {
      this.log('error', 'Erro ao salvar configurações de notificação', error);
    }
  }

  public static getInstance(options?: NotificationServiceOptions): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService(options);
    }
    return NotificationService.instance;
  }

  /**
   * Configurar função de toast
   */
  public setToastFunction(toastFn: (options: any) => void): void {
    this.toastFunction = toastFn;
  }

  /**
   * Configurar cliente Supabase
   */
  public setSupabaseClient(client: any): void {
    this.supabaseClient = client;
  }

  /**
   * Habilitar ou desabilitar sons de notificação
   */
  public setSoundsEnabled(enabled: boolean): void {
    this.soundsEnabled = enabled;
    this.saveSettings();
  }

  /**
   * Verificar se sons de notificação estão habilitados
   */
  public areSoundsEnabled(): boolean {
    return this.soundsEnabled && this.enableSounds;
  }

  /**
   * Mostrar notificação
   */
  public async showNotification(options: NotificationOptions): Promise<void> {
    const {
      title,
      message,
      type = 'info',
      sound,
      autoClose = true,
      duration = this.defaultDuration,
      onClick,
      onClose,
      metadata
    } = options;

    // Determinar som com base no tipo de notificação
    const soundType = sound || DEFAULT_SOUND_MAPPING[type] || 'default';

    // Mostrar toast se disponível
    if (this.toastFunction) {
      this.toastFunction({
        title,
        description: message,
        variant: type === 'error' ? 'destructive' : type === 'success' ? 'success' : 'default',
        duration: autoClose ? duration : undefined,
        action: onClick ? { label: 'Ver', onClick } : undefined,
        onClose
      });
    } else {
      // Fallback para console se toast não estiver disponível
      this.log('info', `${title}: ${message}`, { type, metadata });
    }

    // Reproduzir som se habilitado
    if (this.areSoundsEnabled()) {
      try {
        await audioService.playNotificationSound(soundType);
      } catch (error) {
        this.log('error', 'Erro ao reproduzir som de notificação', error);
      }
    }
  }

  /**
   * Criar notificação no banco de dados
   */
  public async createNotification(userId: string, options: NotificationOptions): Promise<Notification | null> {
    if (!this.supabaseClient) {
      this.log('error', 'Cliente Supabase não configurado');
      return null;
    }

    try {
      const { title, message, type = 'info', metadata } = options;

      const notificationData = {
        user_id: userId,
        title,
        message,
        type,
        is_read: false,
        created_at: new Date().toISOString(),
        metadata
      };

      const { data, error } = await this.supabaseClient
        .from('notifications')
        .insert(notificationData)
        .select()
        .single();

      if (error) {
        this.log('error', 'Erro ao criar notificação', { error, notificationData });
        return null;
      }

      this.log('debug', 'Notificação criada com sucesso', { notification: data });
      return data as Notification;
    } catch (error) {
      this.log('error', 'Erro ao criar notificação', error);
      return null;
    }
  }

  /**
   * Inscrever-se para receber notificações em tempo real
   */
  public subscribeToNotifications(userId: string, onNotification: (notification: Notification) => void): { unsubscribe: () => void } {
    if (!this.supabaseClient) {
      this.log('error', 'Cliente Supabase não configurado');
      return { unsubscribe: () => {} };
    }

    try {
      // Inscrever-se para receber atualizações em tempo real
      const subscription = this.supabaseClient
        .channel(`notifications:${userId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=eq.${userId}`
          },
          (payload: any) => {
            const notification = payload.new as Notification;
            this.log('debug', 'Nova notificação recebida', { notification });
            onNotification(notification);
          }
        )
        .subscribe((status: any) => {
          this.log('debug', 'Status da inscrição de notificações', { status });
        });

      // Retornar função para cancelar a inscrição
      return {
        unsubscribe: () => {
          this.log('debug', 'Cancelando inscrição de notificações', { userId });
          this.supabaseClient.removeChannel(subscription);
        }
      };
    } catch (error) {
      this.log('error', 'Erro ao inscrever-se para notificações', error);
      return { unsubscribe: () => {} };
    }
  }
}

// Exportar função para criar instância
export const createNotificationService = (options?: NotificationServiceOptions) => {
  return NotificationService.getInstance(options);
};

// Exportar instância padrão
const defaultNotificationService = NotificationService.getInstance();
export default defaultNotificationService;
