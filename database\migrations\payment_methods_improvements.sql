-- =====================================================
-- MIGRAÇÃO: PAYMENT_METHODS TABLE IMPROVEMENTS
-- Adiciona colunas necessárias para funcionalidade completa
-- =====================================================

-- 1. Adicionar colunas que estão faltando na tabela payment_methods
ALTER TABLE payment_methods 
ADD COLUMN IF NOT EXISTS name TEXT,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS card_holder_name TEXT,
ADD COLUMN IF NOT EXISTS card_expiry_month TEXT,
ADD COLUMN IF NOT EXISTS card_expiry_year TEXT,
ADD COLUMN IF NOT EXISTS pix_key TEXT,
ADD COLUMN IF NOT EXISTS cash_enabled BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS cash_change_limit DECIMAL(10,2) DEFAULT 50.00,
ADD COLUMN IF NOT EXISTS cash_notes TEXT;

-- 2. Atualizar registros existentes para ter valores padrão
UPDATE payment_methods 
SET 
  name = display_name 
WHERE name IS NULL AND display_name IS NOT NULL;

UPDATE payment_methods 
SET 
  is_active = true 
WHERE is_active IS NULL;

UPDATE payment_methods 
SET 
  cash_enabled = true,
  cash_change_limit = 50.00,
  cash_notes = 'Tenha o valor exato ou próximo. Motoristas podem não ter troco para valores altos.'
WHERE type = 'cash' AND cash_enabled IS NULL;

-- 3. Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_payment_methods_user_id ON payment_methods(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_type ON payment_methods(type);
CREATE INDEX IF NOT EXISTS idx_payment_methods_is_default ON payment_methods(is_default);
CREATE INDEX IF NOT EXISTS idx_payment_methods_is_active ON payment_methods(is_active);

-- 4. Adicionar trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_payment_methods_updated_at ON payment_methods;
CREATE TRIGGER update_payment_methods_updated_at
    BEFORE UPDATE ON payment_methods
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 5. Verificar estrutura final da tabela
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'payment_methods' 
ORDER BY ordinal_position;

-- 6. Verificar dados existentes
SELECT 
    id,
    user_id,
    type,
    name,
    display_name,
    is_default,
    is_active,
    cash_enabled,
    cash_change_limit,
    created_at
FROM payment_methods 
ORDER BY created_at DESC 
LIMIT 10;
