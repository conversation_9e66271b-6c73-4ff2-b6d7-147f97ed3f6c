-- =====================================================
-- MOBIDRIVE - SINCRONIZAÇÃO COMPLETA DO BANCO
-- =====================================================
-- Execute este SQL no dashboard: https://supabase.com/dashboard/project/udquhavmgqtpkubrfzdm/editor
-- =====================================================

-- 1. CRIAR TABELA DRIVER_PROFILES
CREATE TABLE IF NOT EXISTS driver_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    vehicle_type VARCHAR(50) DEFAULT 'economy',
    vehicle_make VARCHAR(100),
    vehicle_model VARCHAR(100),
    vehicle_year INTEGER,
    vehicle_color VARCHAR(50),
    vehicle_plate VARCHAR(20),
    license_number VARCHAR(50),
    license_expiry DATE,
    rating DECIMAL(3, 2) DEFAULT 4.5,
    total_rides INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- 2. CRIAR TABELA DRIVER_NOTIFICATIONS
CREATE TABLE IF NOT EXISTS driver_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    driver_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    ride_id UUID REFERENCES ride_requests(id) ON DELETE CASCADE,
    notification_type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT false,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. CRIAR TABELA RIDE_MATCHING_HISTORY
CREATE TABLE IF NOT EXISTS ride_matching_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    ride_id UUID NOT NULL REFERENCES ride_requests(id) ON DELETE CASCADE,
    driver_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    matching_score DECIMAL(5, 2),
    distance_km DECIMAL(8, 3),
    eta_minutes INTEGER,
    notification_sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    response_received_at TIMESTAMP WITH TIME ZONE,
    response_type VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. CRIAR ÍNDICES
CREATE INDEX IF NOT EXISTS idx_driver_profiles_user_id ON driver_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_driver_profiles_active ON driver_profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_driver_notifications_driver_id ON driver_notifications(driver_id);
CREATE INDEX IF NOT EXISTS idx_ride_matching_history_ride_id ON ride_matching_history(ride_id);

-- 5. HABILITAR RLS
ALTER TABLE driver_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE ride_matching_history ENABLE ROW LEVEL SECURITY;

-- 6. CRIAR POLÍTICAS RLS
CREATE POLICY "Anyone can view active driver profiles" ON driver_profiles
    FOR SELECT USING (is_active = true);

CREATE POLICY "Drivers can insert their own profile" ON driver_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Drivers can update their own profile" ON driver_profiles
    FOR UPDATE USING (auth.uid() = user_id);

-- 7. CONCEDER PERMISSÕES
GRANT ALL ON driver_profiles TO authenticated;
GRANT ALL ON driver_notifications TO authenticated;
GRANT ALL ON ride_matching_history TO authenticated;

-- 8. VERIFICAÇÃO
SELECT 'driver_profiles' as table_name, COUNT(*) as exists FROM information_schema.tables WHERE table_name = 'driver_profiles'
UNION ALL
SELECT 'driver_notifications' as table_name, COUNT(*) as exists FROM information_schema.tables WHERE table_name = 'driver_notifications'
UNION ALL
SELECT 'ride_matching_history' as table_name, COUNT(*) as exists FROM information_schema.tables WHERE table_name = 'ride_matching_history';
