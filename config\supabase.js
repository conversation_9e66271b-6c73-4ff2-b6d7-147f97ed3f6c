import { createClient } from '@supabase/supabase-js'

// Carrega variáveis de ambiente
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Verifica se as variáveis de ambiente estão definidas
if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Variáveis de ambiente do Supabase não configuradas!')
}

// Cria cliente Supabase
const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Constantes para nomes de tabelas
export const TABLES = {
  RIDE_REQUESTS: 'ride_requests',
  DRIVER_LOCATIONS: 'driver_locations',
  DRIVER_PROFILES: 'driver_profiles',
  DRIVER_NOTIFICATIONS: 'driver_notifications',
  RIDE_MATCHING_HISTORY: 'ride_matching_history'
}

// Funções auxiliares para operações comuns
export const supabaseHelper = {
  // Função para verificar conexão
  async testConnection() {
    const { data, error } = await supabase.from(TABLES.RIDE_REQUESTS).select('count').limit(1)
    if (error) throw error
    return true
  },

  // Função para obter usuário atual
  async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser()
    if (error) throw error
    return user
  }
}

export default supabase