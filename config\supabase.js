import { createClient } from '@supabase/supabase-js'

// Configuração centralizada do Supabase
const SUPABASE_CONFIG = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.VITE_SUPABASE_URL,
  anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY,
  options: {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
      storageKey: 'mobdrive-auth-token',
      flowType: 'pkce'
    },
    realtime: {
      autoconnect: false // Conectar apenas quando necessário
    },
    db: {
      schema: 'public'
    }
  }
}

// Verifica se as variáveis de ambiente estão definidas
if (!SUPABASE_CONFIG.url || !SUPABASE_CONFIG.anonKey) {
  console.error('❌ Variáveis de ambiente do Supabase não configuradas!')
  console.error('Configure NEXT_PUBLIC_SUPABASE_URL e NEXT_PUBLIC_SUPABASE_ANON_KEY')
  throw new Error('Variáveis de ambiente do Supabase não configuradas!')
}

// Cria cliente Supabase
const supabase = createClient(
  SUPABASE_CONFIG.url,
  SUPABASE_CONFIG.anonKey,
  SUPABASE_CONFIG.options
)

// Constantes padronizadas para nomes de tabelas
export const TABLES = {
  // Tabelas principais
  RIDE_REQUESTS: 'ride_requests',
  DRIVER_LOCATIONS: 'driver_locations',
  DRIVER_PROFILES: 'driver_profiles',
  PROFILES: 'profiles',

  // Tabelas de notificação e comunicação
  NOTIFICATIONS: 'notifications',
  CHAT_MESSAGES: 'chat_messages',

  // Tabelas de pagamento
  PAYMENTS: 'payments',
  PAYMENT_METHODS: 'payment_methods',

  // Configurações
  APP_SETTINGS: 'app_settings'
}

// Buckets de armazenamento
export const STORAGE_BUCKETS = {
  PROFILE_IMAGES: 'profile_images',
  DOCUMENTS: 'documents',
  RECORDINGS: 'recordings'
}

// Funções auxiliares para operações comuns
export const supabaseHelper = {
  // Função para verificar conexão
  async testConnection() {
    try {
      const { data, error } = await supabase
        .from(TABLES.RIDE_REQUESTS)
        .select('count')
        .limit(1)

      if (error) throw error
      return { success: true, connected: true }
    } catch (error) {
      console.error('❌ Erro na conexão com Supabase:', error)
      return { success: false, connected: false, error }
    }
  },

  // Função para obter usuário atual
  async getCurrentUser() {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()
      if (error) throw error
      return { success: true, user }
    } catch (error) {
      console.error('❌ Erro ao obter usuário:', error)
      return { success: false, user: null, error }
    }
  },

  // Função para verificar saúde do sistema
  async getSystemHealth() {
    const connectionTest = await this.testConnection()
    const userTest = await this.getCurrentUser()

    return {
      timestamp: new Date().toISOString(),
      connection: connectionTest,
      auth: userTest,
      overall: connectionTest.success ? 'healthy' : 'unhealthy'
    }
  }
}

export { SUPABASE_CONFIG }
export default supabase