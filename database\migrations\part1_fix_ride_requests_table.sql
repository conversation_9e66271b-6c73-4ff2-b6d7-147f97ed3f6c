-- Part 1: Fix ride_requests table structure
-- Basic table structure and indexes

-- Drop existing ride_requests table if it exists
DROP TABLE IF EXISTS ride_requests CASCADE;

-- Create ride_requests table with correct structure
CREATE TABLE ride_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    driver_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    
    -- Standardize location fields
    pickup_latitude DECIMAL(10, 8) NOT NULL,
    pickup_longitude DECIMAL(11, 8) NOT NULL,
    pickup_address TEXT NOT NULL,
    
    destination_latitude DECIMAL(10, 8) NOT NULL,
    destination_longitude DECIMAL(11, 8) NOT NULL,
    destination_address TEXT NOT NULL,
    
    -- Ride details
    vehicle_type VARCHAR(50) DEFAULT 'economy',
    estimated_distance DECIMAL(10, 2),
    estimated_duration INTEGER,
    estimated_price DECIMAL(10, 2),
    final_price DECIMAL(10, 2),
    
    -- Payment info
    payment_method VARCHAR(50) DEFAULT 'cash',
    payment_status VARCHAR(50) DEFAULT 'pending',
    
    -- Status tracking
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    
    -- Additional fields
    cancellation_reason TEXT,
    notes TEXT
);

-- Add necessary indexes
CREATE INDEX idx_ride_requests_user_id ON ride_requests(user_id);
CREATE INDEX idx_ride_requests_driver_id ON ride_requests(driver_id);
CREATE INDEX idx_ride_requests_status ON ride_requests(status);
CREATE INDEX idx_ride_requests_created_at ON ride_requests(created_at);
CREATE INDEX idx_ride_requests_pickup_coords ON ride_requests(pickup_latitude, pickup_longitude);

-- Add trigger for updating updated_at
CREATE TRIGGER update_ride_requests_updated_at
    BEFORE UPDATE ON ride_requests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();