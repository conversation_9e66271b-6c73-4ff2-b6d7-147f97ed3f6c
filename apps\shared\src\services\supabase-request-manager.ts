/**
 * Gerenciador de requisições Supabase
 * Controla a concorrência e limita o número de requisições simultâneas
 */

// Tipo de uma tarefa
interface Task {
  id: string;
  fn: () => Promise<any>;
  priority: number;
  timestamp: number;
  resolve: (value: any) => void;
  reject: (error: any) => void;
}

/**
 * Classe para gerenciar requisições ao Supabase
 */
class SupabaseRequestManager {
  private static instance: SupabaseRequestManager;
  private queue: Task[] = [];
  private runningTasks: Set<string> = new Set();
  private maxConcurrent: number = 8;
  private isProcessing: boolean = false;
  private pauseProcessing: boolean = false;
  private taskIdCounter: number = 0;
  
  // Singleton pattern
  public static getInstance(): SupabaseRequestManager {
    if (!SupabaseRequestManager.instance) {
      SupabaseRequestManager.instance = new SupabaseRequestManager();
    }
    return SupabaseRequestManager.instance;
  }
  
  private constructor() {
    // Iniciar processamento da fila
    this.processQueue();
    
    // Monitorar eventos de conexão
    if (typeof window !== 'undefined') {
      window.addEventListener('online', this.handleOnline.bind(this));
      window.addEventListener('offline', this.handleOffline.bind(this));
    }
  }
  
  /**
   * Lidar com evento online
   */
  private handleOnline(): void {
    console.log('🌐 Conexão restaurada, retomando processamento de requisições...');
    this.pauseProcessing = false;
    this.processQueue();
  }
  
  /**
   * Lidar com evento offline
   */
  private handleOffline(): void {
    console.log('🔌 Conexão perdida, pausando processamento de requisições...');
    this.pauseProcessing = true;
  }
  
  /**
   * Processar fila de tarefas
   */
  private async processQueue(): Promise<void> {
    // Evitar múltiplas instâncias do processador
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    
    while (true) {
      // Verificar se o processamento está pausado
      if (this.pauseProcessing) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        continue;
      }
      
      // Verificar se há tarefas que podem ser executadas
      if (this.queue.length === 0 || this.runningTasks.size >= this.maxConcurrent) {
        await new Promise(resolve => setTimeout(resolve, 100));
        continue;
      }
      
      // Ordenar fila por prioridade e timestamp
      this.queue.sort((a, b) => {
        if (a.priority !== b.priority) {
          return a.priority - b.priority; // Menor número = maior prioridade
        }
        return a.timestamp - b.timestamp; // Mais antiga primeiro
      });
      
      // Obter próxima tarefa
      const task = this.queue.shift();
      
      if (!task) continue;
      
      // Adicionar à lista de tarefas em execução
      this.runningTasks.add(task.id);
      
      // Executar tarefa em segundo plano
      this.executeTask(task).finally(() => {
        // Remover da lista de tarefas em execução
        this.runningTasks.delete(task.id);
      });
    }
  }
  
  /**
   * Executar uma tarefa
   */
  private async executeTask(task: Task): Promise<void> {
    try {
      const result = await task.fn();
      task.resolve(result);
    } catch (error) {
      task.reject(error);
    }
  }
  
  /**
   * Enfileirar uma tarefa
   */
  public enqueue<T>(
    fn: () => Promise<T>,
    options: {
      priority?: number; // 1 = alta, 2 = média, 3 = baixa
      timeout?: number;
    } = {}
  ): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      // Gerar ID único para a tarefa
      const id = `task_${this.taskIdCounter++}_${Date.now()}`;
      
      // Criar tarefa
      const task: Task = {
        id,
        fn,
        priority: options.priority || 2,
        timestamp: Date.now(),
        resolve,
        reject
      };
      
      // Adicionar à fila
      this.queue.push(task);
      
      // Configurar timeout se necessário
      if (options.timeout) {
        setTimeout(() => {
          // Verificar se a tarefa ainda está na fila
          const index = this.queue.findIndex(t => t.id === id);
          if (index >= 0) {
            // Remover da fila
            this.queue.splice(index, 1);
            reject(new Error('Timeout'));
          }
        }, options.timeout);
      }
    });
  }
  
  /**
   * Definir o número máximo de requisições concorrentes
   */
  public setMaxConcurrent(max: number): void {
    this.maxConcurrent = max;
  }
  
  /**
   * Obter o número de tarefas na fila
   */
  public getQueueSize(): number {
    return this.queue.length;
  }
  
  /**
   * Obter o número de tarefas em execução
   */
  public getRunningTasksCount(): number {
    return this.runningTasks.size;
  }
  
  /**
   * Obter estatísticas
   */
  public getStats(): {
    queueSize: number;
    runningTasks: number;
    maxConcurrent: number;
    isPaused: boolean;
  } {
    return {
      queueSize: this.queue.length,
      runningTasks: this.runningTasks.size,
      maxConcurrent: this.maxConcurrent,
      isPaused: this.pauseProcessing
    };
  }
  
  /**
   * Limpar fila
   */
  public clearQueue(): void {
    // Rejeitar todas as tarefas na fila
    this.queue.forEach(task => {
      task.reject(new Error('Queue cleared'));
    });
    
    this.queue = [];
  }
}

// Exportar instância única
export const requestManager = SupabaseRequestManager.getInstance();

/**
 * Função para envolver uma função com o gerenciador de requisições
 */
export function withRequestManager<T>(
  fn: () => Promise<T>,
  options: {
    priority?: number;
    timeout?: number;
  } = {}
): Promise<T> {
  return requestManager.enqueue(fn, options);
}

export default requestManager;