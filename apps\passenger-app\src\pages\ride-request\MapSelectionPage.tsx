import React, { useState, useEffect, useCallback, useRef } from 'react'
import { motion } from 'framer-motion'
import { Navigate, useNavigate } from 'react-router-dom'
import mapboxgl from 'mapbox-gl'
import { 
  ArrowLeft,
  Search,
  Target,
  Mic,
  MicOff,
  Loader2,
  CheckCircle,
  MapPin
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContextSimple'
import { useMapboxSearch } from '../../hooks/useMapboxSearch'
import { useNoZoom } from '../../hooks/useNoZoom'
import 'mapbox-gl/dist/mapbox-gl.css'
import '../../styles/no-zoom.css'

// Configure Mapbox token from environment
const MAPBOX_TOKEN = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN
if (!MAPBOX_TOKEN) {
  console.error('❌ VITE_MAPBOX_ACCESS_TOKEN não configurado!')
}
mapboxgl.accessToken = MAPBOX_TOKEN

export const MapSelectionPage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuth()
  
  // State management
  const [currentLocation, setCurrentLocation] = useState<[number, number] | null>(null)

  // Hooks with user location
  const {
    searchQuery,
    searchResults,
    isSearching,
    searchPlaces,
    selectResult,
    getCurrentLocation
  } = useMapboxSearch({ userLocation: currentLocation || undefined })

  useNoZoom()

  // Redirect if not logged in
  if (!user) {
    return <Navigate to="/login" replace />
  }

  // Additional state management
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [destination, setDestination] = useState<any>(null)
  const [isListening, setIsListening] = useState(false)
  const [speechSupported, setSpeechSupported] = useState(false)

  // Map states
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<mapboxgl.Map | null>(null)
  const [isMapLoaded, setIsMapLoaded] = useState(false)
  const destinationMarker = useRef<mapboxgl.Marker | null>(null)
  const userMarker = useRef<mapboxgl.Marker | null>(null)



  // Reverse geocoding for address preview
  const reverseGeocode = useCallback(async (coordinates: [number, number]) => {
    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${coordinates[0]},${coordinates[1]}.json?access_token=${MAPBOX_TOKEN}&language=pt-BR&limit=1`
      )
      const data = await response.json()
      if (data.features && data.features.length > 0) {
        return data.features[0].place_name
      }
    } catch (error) {
      console.warn('Reverse geocoding failed:', error)
    }
    return `${coordinates[1].toFixed(4)}, ${coordinates[0].toFixed(4)}`
  }, [])

  // Create draggable destination marker with route projection
  const createDestinationMarker = useCallback((coordinates: [number, number]) => {
    // Remove existing marker
    if (destinationMarker.current) {
      destinationMarker.current.remove()
    }

    // Create draggable marker
    destinationMarker.current = new mapboxgl.Marker({
      color: '#ef4444',
      scale: 1.2,
      draggable: true
    })
      .setLngLat(coordinates)
      .addTo(map.current!)

    // Add drag event listeners
    destinationMarker.current.on('dragstart', () => {
      // Remove route while dragging
      if (map.current?.getSource('route')) {
        map.current.removeLayer('route')
        map.current.removeSource('route')
      }
    })

    destinationMarker.current.on('dragend', async () => {
      const lngLat = destinationMarker.current!.getLngLat()
      const coords: [number, number] = [lngLat.lng, lngLat.lat]
      
      // Update destination with new coordinates
      const address = await reverseGeocode(coords)
      const destinationObj = {
        id: `destination-${Date.now()}`,
        place_name: address,
        center: coords,
        geometry: {
          type: 'Point' as const,
          coordinates: coords
        },
        properties: {},
        context: []
      }

      setDestination(destinationObj)
      
      // Project route to new destination
      await projectRoute(currentLocation!, coords)
    })

    return destinationMarker.current
  }, [reverseGeocode, currentLocation])

  // Project route between origin and destination using Mapbox Directions API
  const projectRoute = useCallback(async (origin: [number, number], destination: [number, number]) => {
    console.log('🚀 ProjectRoute called with:', { origin, destination, mapReady: !!map.current })

    if (!map.current) {
      console.warn('⚠️ Map not available')
      return
    }

    if (!origin || !destination) {
      console.warn('⚠️ Missing origin or destination coordinates', { origin, destination })
      return
    }

    console.log('🗺️ Projecting route from:', origin, 'to:', destination)

    try {
      // Use Mapbox Directions API with correct format
      const directionsUrl = `https://api.mapbox.com/directions/v5/mapbox/driving/${origin[0]},${origin[1]};${destination[0]},${destination[1]}?steps=true&geometries=geojson&access_token=${MAPBOX_TOKEN}`

      console.log('🌐 Calling Directions API')
      console.log('🔗 API URL:', directionsUrl.replace(MAPBOX_TOKEN, 'TOKEN_HIDDEN'))

      const response = await fetch(directionsUrl)

      console.log('📡 Response status:', response.status, response.statusText)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ API Error Response:', errorText)
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      console.log('📡 Directions API response:', {
        code: data.code,
        routesFound: data.routes?.length || 0,
        firstRouteGeometry: data.routes?.[0]?.geometry ? 'Present' : 'Missing',
        coordinatesCount: data.routes?.[0]?.geometry?.coordinates?.length || 0,
        fullGeometry: data.routes?.[0]?.geometry
      })

      if (data.code === 'Ok' && data.routes && data.routes.length > 0) {
        const route = data.routes[0]

        console.log('🛣️ Route found:', {
          distance: `${(route.distance / 1000).toFixed(1)}km`,
          duration: `${Math.round(route.duration / 60)}min`,
          geometryType: route.geometry?.type,
          coordinatesCount: route.geometry?.coordinates?.length
        })

        // Clean up existing route first
        if (map.current.getSource('route')) {
          if (map.current.getLayer('route')) {
            map.current.removeLayer('route')
          }
          map.current.removeSource('route')
        }

        // Add route source - CRITICAL: Use exact format from working example
        map.current.addSource('route', {
          type: 'geojson',
          data: {
            type: 'Feature',
            geometry: route.geometry
          }
        })

        // Add route layer - CRITICAL: Use exact format from working example
        map.current.addLayer({
          id: 'route',
          type: 'line',
          source: 'route',
          layout: {
            'line-join': 'round',
            'line-cap': 'round'
          },
          paint: {
            'line-color': '#3b9ddd',
            'line-width': 6
          }
        })

        console.log('✅ Route added to map successfully!')

        // Fit bounds to show the route
        const coordinates = route.geometry.coordinates
        const bounds = coordinates.reduce(
          (bounds: any, coord: any) => bounds.extend(coord),
          new mapboxgl.LngLatBounds()
        )

        map.current.fitBounds(bounds, {
          padding: 50
        })

        console.log('🎯 Map fitted to route bounds')

      } else {
        console.warn('⚠️ No routes found in API response:', data)

        // Clean up existing route
        if (map.current.getSource('route')) {
          if (map.current.getLayer('route')) {
            map.current.removeLayer('route')
          }
          map.current.removeSource('route')
        }

        // Add fallback straight line
        map.current.addSource('route', {
          type: 'geojson',
          data: {
            type: 'Feature',
            geometry: {
              type: 'LineString',
              coordinates: [origin, destination]
            }
          }
        })

        map.current.addLayer({
          id: 'route',
          type: 'line',
          source: 'route',
          layout: {
            'line-join': 'round',
            'line-cap': 'round'
          },
          paint: {
            'line-color': '#ef4444',
            'line-width': 4,
            'line-dasharray': [2, 2]
          }
        })

        // Fit bounds
        const bounds = new mapboxgl.LngLatBounds()
        bounds.extend(origin)
        bounds.extend(destination)

        map.current.fitBounds(bounds, {
          padding: 50
        })

        console.log('🔴 Added fallback straight line')
      }

    } catch (error) {
      console.error('❌ Failed to project route:', error)

      // Clean up existing route
      if (map.current?.getSource('route')) {
        if (map.current.getLayer('route')) {
          map.current.removeLayer('route')
        }
        map.current.removeSource('route')
      }

      // Add error fallback route
      map.current?.addSource('route', {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'LineString',
            coordinates: [origin, destination]
          }
        }
      })

      map.current?.addLayer({
        id: 'route',
        type: 'line',
        source: 'route',
        layout: {
          'line-join': 'round',
          'line-cap': 'round'
        },
        paint: {
          'line-color': '#f59e0b',
          'line-width': 4,
          'line-dasharray': [3, 3]
        }
      })

      // Fit bounds
      const bounds = new mapboxgl.LngLatBounds()
      bounds.extend(origin)
      bounds.extend(destination)

      map.current?.fitBounds(bounds, {
        padding: 50
      })

      console.log('🟠 Added error fallback route')
    }
  }, [isMapLoaded])

  // Initialize map
  const initializeMap = useCallback((coords: [number, number]) => {
    if (!mapContainer.current || map.current) return

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/dark-v11',
      center: coords,
      zoom: 14,
      attributionControl: false,
      antialias: true,
      pitch: 0,
      bearing: 0
    })

    map.current.on('load', () => {
      setIsMapLoaded(true)
      
      // Add user location marker with custom styling
      const userElement = document.createElement('div')
      userElement.innerHTML = `
        <div style="
          width: 20px;
          height: 20px;
          background: #3b82f6;
          border: 3px solid white;
          border-radius: 50%;
          box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
        "></div>
      `
      
      userMarker.current = new mapboxgl.Marker({
        element: userElement
      })
        .setLngLat(coords)
        .addTo(map.current!)

      // Add click handler for destination selection
      map.current!.on('click', (e) => {
        const coordinates: [number, number] = [e.lngLat.lng, e.lngLat.lat]
        handleMapClick(coordinates)
      })

      // Add cursor change on hover
      map.current!.on('mouseenter', () => {
        map.current!.getCanvas().style.cursor = 'crosshair'
      })

      map.current!.on('mouseleave', () => {
        map.current!.getCanvas().style.cursor = ''
      })
    })

    // Add navigation controls positioned to avoid UI overlap
    map.current.addControl(new mapboxgl.NavigationControl(), 'bottom-right')
  }, [])

  // Handle map click for destination selection
  const handleMapClick = useCallback(async (coordinates: [number, number]) => {
    console.log('🖱️ Map clicked at coordinates:', coordinates)
    console.log('📍 Current location available:', !!currentLocation)

    // Create draggable marker
    createDestinationMarker(coordinates)

    // Get address for the location
    const address = await reverseGeocode(coordinates)
    console.log('🏠 Address resolved:', address)

    // Create destination object
    const destinationObj = {
      id: `destination-${Date.now()}`,
      place_name: address,
      center: coordinates,
      geometry: {
        type: 'Point' as const,
        coordinates
      },
      properties: {},
      context: []
    }

    setDestination(destinationObj)
    console.log('🎯 Destination set:', destinationObj)

    // Project route from current location to destination
    if (currentLocation) {
      console.log('🗺️ About to call projectRoute with:', { from: currentLocation, to: coordinates })
      await projectRoute(currentLocation, coordinates)
    } else {
      console.warn('⚠️ Cannot project route: currentLocation is null')
    }
  }, [createDestinationMarker, reverseGeocode, currentLocation, projectRoute])

  // Get user location on mount and initialize map
  useEffect(() => {
    const initLocation = async () => {
      try {
        console.log('🌍 MapSelectionPage: Getting user location...')
        const coords = await getCurrentLocation()
        console.log('📍 MapSelectionPage: User location obtained:', coords)
        setCurrentLocation(coords)
        initializeMap(coords)
      } catch (error) {
        console.warn('⚠️ MapSelectionPage: Could not get user location:', error)
        // Fallback to São Paulo
        const fallbackCoords: [number, number] = [-46.6333, -23.5505]
        console.log('🏙️ MapSelectionPage: Using fallback location (São Paulo):', fallbackCoords)
        setCurrentLocation(fallbackCoords)
        initializeMap(fallbackCoords)
      }
    }
    initLocation()
  }, [getCurrentLocation, initializeMap])

  // Debug: Log when currentLocation changes
  useEffect(() => {
    console.log('🎯 MapSelectionPage: currentLocation changed to:', currentLocation)
  }, [currentLocation])

  // Initialize speech recognition
  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      setSpeechSupported(true)
    }
  }, [])



  // Cleanup map on unmount
  useEffect(() => {
    return () => {
      if (map.current) {
        map.current.remove()
      }
    }
  }, [])

  // Voice search functionality
  const startVoiceSearch = useCallback(() => {
    if (!speechSupported) return

    setIsListening(true)
    const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition
    const recognition = new SpeechRecognition()

    recognition.lang = 'pt-BR'
    recognition.continuous = false
    recognition.interimResults = false

    recognition.onresult = (event) => {
      const transcript = event.results[0][0].transcript
      searchPlaces(transcript)
      setIsListening(false)
    }

    recognition.onerror = () => {
      setIsListening(false)
      setError('Erro no reconhecimento de voz')
    }

    recognition.onend = () => {
      setIsListening(false)
    }

    recognition.start()
  }, [speechSupported, searchPlaces])

  // Event handlers
  const handleDestinationSelect = useCallback(async (place: any) => {
    console.log('🎯 Destination selected from search:', place)
    setDestination(place)
    selectResult(place)

    // Add draggable marker to map
    if (map.current && place.center) {
      createDestinationMarker(place.center)

      // Project route from current location to destination
      if (currentLocation) {
        console.log('🗺️ About to call projectRoute from search selection:', { from: currentLocation, to: place.center })
        await projectRoute(currentLocation, place.center)
      } else {
        console.warn('⚠️ Cannot project route from search: currentLocation is null')
      }

      // Center map on destination with smooth animation
      map.current.flyTo({
        center: place.center,
        zoom: 16,
        duration: 1000,
        essential: true
      })
    }
  }, [selectResult, createDestinationMarker, currentLocation, projectRoute])

  const handleConfirmDestination = useCallback(async () => {
    if (destination && currentLocation) {
      // Get real address for current location
      let originAddress = 'Sua localização atual'
      try {
        originAddress = await reverseGeocode(currentLocation)
        console.log('🏠 Origin address resolved:', originAddress)
      } catch (error) {
        console.warn('⚠️ Failed to get origin address:', error)
      }

      // Store destination in sessionStorage for next page
      sessionStorage.setItem('rideDestination', JSON.stringify(destination))
      sessionStorage.setItem('rideOrigin', JSON.stringify({
        center: currentLocation,
        place_name: originAddress
      }))

      // Navigate to trip details page
      navigate('/ride-request/details')
    }
  }, [destination, currentLocation, navigate, reverseGeocode])

  return (
    <div className="no-zoom-page w-full h-full min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-black/40"></div>
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

      <div className="relative z-10 flex flex-col h-full min-h-screen">
        {/* Header */}
        <motion.div
          className="pt-8 pb-6 px-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="flex items-center justify-between">
            <motion.button
              onClick={() => navigate('/dashboard')}
              className="p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-white"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <ArrowLeft className="w-5 h-5" />
            </motion.button>

            <div className="flex-1 text-center">
              <h1 className="text-2xl font-bold text-white">
                MOBI<span className="text-blue-400">DRIVE</span>
              </h1>
              <p className="text-sm text-white/70">Selecione o destino</p>
            </div>

            <div className="w-9"></div>
          </div>
        </motion.div>

        {/* Content */}
        <div className="flex-1 px-4 pb-8">
          {/* Map Container */}
          <div className="relative h-[70vh] rounded-2xl overflow-hidden border border-white/20 shadow-2xl bg-gray-900">
            {/* Map Container */}
            <div ref={mapContainer} className="w-full h-full" />

            {/* Search Box Overlay */}
            <div className="absolute top-4 left-4 right-4 z-10">
              <div className="bg-white/10 backdrop-blur-md rounded-xl p-3 border border-white/20 shadow-lg">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => searchPlaces(e.target.value)}
                    placeholder="Para onde vamos?"
                    className="w-full pl-10 pr-16 py-3 bg-white/10 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"
                  />

                  {/* Voice Search Button */}
                  {speechSupported && (
                    <button
                      onClick={startVoiceSearch}
                      disabled={isListening}
                      className="absolute right-8 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors disabled:opacity-50"
                    >
                      {isListening ? (
                        <MicOff className="w-4 h-4 text-red-400 animate-pulse" />
                      ) : (
                        <Mic className="w-4 h-4" />
                      )}
                    </button>
                  )}

                  {/* Loading Indicator */}
                  {isSearching && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
                    </div>
                  )}
                </div>

                {/* Search Results - Ordered by Distance */}
                {searchResults.length > 0 && (
                  <div className="mt-3 bg-white/10 rounded-lg border border-white/20 overflow-hidden max-h-48 overflow-y-auto">
                    {searchResults.map((result, index) => (
                      <button
                        key={index}
                        onClick={() => handleDestinationSelect(result)}
                        className="w-full text-left px-3 py-2 text-white hover:bg-white/10 transition-colors border-b border-white/10 last:border-b-0"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2 flex-1">
                            <MapPin className="w-3 h-3 text-white/60 flex-shrink-0" />
                            <div className="flex-1">
                              <p className="text-sm font-medium">{result.text}</p>
                              <p className="text-xs text-white/60 truncate">{result.place_name}</p>
                            </div>
                          </div>
                          {result.distance && (
                            <div className="text-xs text-white/50 ml-2">
                              {result.distance < 1
                                ? `${Math.round(result.distance * 1000)}m`
                                : `${result.distance.toFixed(1)}km`
                              }
                            </div>
                          )}
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Selection Mode Indicator */}
            {!destination && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10">
                <div className="bg-black/60 backdrop-blur-sm rounded-full px-4 py-2">
                  <span className="text-white text-sm">Toque no mapa</span>
                </div>
              </div>
            )}

            {/* Map Loading Indicator */}
            {!isMapLoaded && (
              <div className="absolute inset-0 bg-gray-900/80 flex items-center justify-center z-30">
                <div className="text-center text-white">
                  <Loader2 className="w-12 h-12 animate-spin mx-auto mb-4" />
                  <p className="text-lg font-medium">Carregando mapa...</p>
                  <p className="text-sm text-white/70">Aguarde um momento</p>
                </div>
              </div>
            )}
          </div>

          {/* Bottom UI Panel - Only show when destination is selected */}
          {destination && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6"
            >
              <div className="space-y-4">
                {/* Simple Destination Card */}
                <div className="bg-white/10 backdrop-blur-md rounded-xl p-4 border border-white/20 shadow-2xl">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                        <MapPin className="w-4 h-4 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-white text-sm font-medium line-clamp-1">{destination.place_name}</p>
                      </div>
                    </div>
                    <button
                      onClick={() => {
                        setDestination(null)
                        if (destinationMarker.current) {
                          destinationMarker.current.remove()
                          destinationMarker.current = null
                        }
                        // Remove route
                        if (map.current?.getSource('route')) {
                          map.current.removeLayer('route')
                          map.current.removeSource('route')
                        }
                      }}
                      className="text-white/60 hover:text-white transition-colors p-1"
                    >
                      ✕
                    </button>
                  </div>
                </div>

                {/* Simple Confirm Button */}
                <motion.button
                  onClick={handleConfirmDestination}
                  className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Confirmar destino
                </motion.button>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  )
}

export default MapSelectionPage
