/**
 * Exportação centralizada e simplificada dos serviços Supabase
 * Versão limpa e otimizada
 */

// Cliente e configurações básicas
export {
  supabase,
  TABLES,
  STORAGE_BUCKETS,
  supabaseHelpers
} from './supabase-client';

/**
 * Função simplificada para inicializar o Supabase
 */
export function initializeSupabase(options: {
  enableRealtime?: boolean;
  checkConnection?: boolean;
} = {}) {
  const {
    enableRealtime = false,
    checkConnection = true
  } = options;

  // Verificar conexão inicial se solicitado
  if (checkConnection) {
    supabaseHelpers.checkConnection().then(({ connected, latency }) => {
      if (connected) {
        console.log(`✅ Supabase conectado (${latency}ms)`);
      } else {
        console.warn('⚠️ Falha na conexão inicial com Supabase');
      }
    });
  }

  // Conectar realtime se solicitado
  if (enableRealtime && typeof window !== 'undefined') {
    try {
      supabase.realtime.connect();
      console.log('🔴 Realtime conectado');
    } catch (error) {
      console.warn('⚠️ Erro ao conectar realtime:', error);
    }
  }

  console.log('✅ Supabase inicializado');
}

// Cliente padrão para conveniência
export default supabase;