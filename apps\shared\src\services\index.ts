/**
 * Exportação centralizada de serviços Supabase
 * Fornece acesso fácil a todos os serviços relacionados ao Supabase
 */

// Cliente e configurações básicas
export { supabase, TABLES, STORAGE_BUCKETS, supabaseHelpers } from './supabase-client';

// Gerenciadores e monitores
export { realtimeManager } from './supabase-realtime-manager';
export { healthMonitor, HealthStatus, useSupabaseHealth } from './supabase-health-monitor';
export { supabaseErrorHandler, SupabaseErrorType, safeSupabaseOperation } from './supabase-error-handler';
export { syncService } from './supabase-sync-service';
export { functionsService } from './supabase-functions-service';
export { requestManager, withRequestManager } from './supabase-request-manager';

// Hooks úteis
export { 
  useSupabaseQuery, 
  useSupabaseSelect,
  useSupabaseInsert,
  useSupabaseUpdate
} from '../hooks/useSupabaseQuery';

export { 
  useSupabaseRealtime,
  useSupabaseTable,
  useSupabaseBroadcast,
  useLocationUpdates
} from '../hooks/useSupabaseRealtime';

// Componentes
export { SupabaseConnectionStatus } from '../components/SupabaseConnectionStatus';
export { SyncStatusIndicator } from '../components/SyncStatusIndicator';

/**
 * Função para inicializar todos os serviços Supabase
 * Ideal para chamar no início do aplicativo
 */
export function initializeSupabaseServices(options: {
  enableRealtime?: boolean;
  enableHealthMonitor?: boolean;
  enableSyncService?: boolean;
  maxConcurrentRequests?: number;
} = {}) {
  const {
    enableRealtime = true,
    enableHealthMonitor = true,
    enableSyncService = true,
    maxConcurrentRequests = 8
  } = options;
  
  // Configurar gerenciador de requisições
  requestManager.setMaxConcurrent(maxConcurrentRequests);
  
  // Iniciar serviços
  if (enableRealtime) {
    realtimeManager.connect();
  }
  
  if (enableHealthMonitor) {
    healthMonitor.checkHealth();
  }
  
  if (enableSyncService) {
    syncService.sync();
  }
  
  // Registrar eventos globais para capturar erros do Supabase
  if (typeof window !== 'undefined') {
    window.addEventListener('error', (event) => {
      if (event.message?.includes('supabase') || event.filename?.includes('supabase')) {
        supabaseErrorHandler.handleError(event.error);
      }
    });
    
    window.addEventListener('unhandledrejection', (event) => {
      const error = event.reason;
      if (error?.message?.includes('supabase') || error?.stack?.includes('supabase')) {
        supabaseErrorHandler.handleError(error);
      }
    });
  }
  
  console.log('✅ Serviços Supabase inicializados');
}

// Cliente padrão para conveniência
export default supabase;