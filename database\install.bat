@echo off
echo Configuracao do Sistema MobiDrive
echo ================================
echo.

REM Solicitar credenciais do usuario
set /p PGHOST="Digite o host do Supabase (ex: db.xxxxx.supabase.co): "
set /p PGUSER="Digite o usuario do banco (padrao: postgres): "
set /p PGPASSWORD="Digite a senha do banco: "
set PGDATABASE=postgres

echo.
echo Validando conexao com o banco de dados...
echo.

REM Testar conexao
psql -h %PGHOST% -U %PGUSER% -d %PGDATABASE% -c "\conninfo" >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Erro: Nao foi possivel conectar ao banco de dados.
    echo Verifique suas credenciais e tente novamente.
    pause
    exit /b 1
)

echo Conexao estabelecida com sucesso!
echo.
echo Iniciando instalacao...
echo.

REM Executar o script de instalacao
psql -h %PGHOST% -U %PGUSER% -d %PGDATABASE% -f install.sql

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Erro durante a instalacao. Verifique as mensagens acima.
    pause
    exit /b 1
)

echo.
echo Instalacao concluida com sucesso!
echo.
echo Para comecar a usar o sistema:
echo 1. Verifique se nao ha erros nas mensagens acima
echo 2. Teste o sistema usando o aplicativo
echo 3. Em caso de problemas, verifique os logs
echo.
pause