#!/usr/bin/env node

/**
 * Script para criar tabelas faltantes no Supabase
 * Usa as mesmas credenciais que já estão funcionando
 */

import { createClient } from '@supabase/supabase-js';

// Usar as mesmas credenciais que já funcionam
const supabaseUrl = 'https://udquhavmgqtpkubrfzdm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzU2NzI4NzQsImV4cCI6MjA1MTI0ODg3NH0.KrCJ_WZ_79a92hFSISIq4g';

const supabase = createClient(supabaseUrl, supabaseKey);

// Função para verificar se uma tabela existe
async function tableExists(tableName) {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('count')
      .limit(1);

    return !error;
  } catch (error) {
    return false;
  }
}

// Função para executar SQL usando uma abordagem alternativa
async function createDriverProfilesTable() {
  console.log('🔧 Tentando criar tabela driver_profiles...');

  try {
    // Vamos tentar inserir dados de teste para forçar a criação da estrutura
    // Se a tabela não existir, isso falhará de forma controlada

    // Primeiro, vamos verificar se podemos acessar auth.users
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers();

    if (usersError) {
      console.log('⚠️ Não é possível acessar auth.users com esta chave');
      console.log('💡 Vamos usar uma abordagem diferente...');

      // Tentar criar usando RPC se existir
      const { data, error } = await supabase.rpc('create_driver_profiles_table');

      if (error) {
        console.log('⚠️ RPC não disponível, tentando abordagem manual...');
        return await createTableManually();
      }

      console.log('✅ Tabela criada via RPC');
      return true;
    }

    console.log('✅ Acesso ao auth.users disponível');
    return await createTableManually();

  } catch (error) {
    console.error('❌ Erro ao criar tabela:', error.message);
    return false;
  }
}

// Função para criar tabela manualmente usando INSERT/UPDATE
async function createTableManually() {
  console.log('🔧 Criando estrutura da tabela driver_profiles...');

  try {
    // Como não podemos executar DDL diretamente, vamos usar o dashboard do Supabase
    console.log('📋 Para criar a tabela driver_profiles, execute o seguinte SQL no dashboard do Supabase:');
    console.log('🌐 Acesse: https://supabase.com/dashboard/project/udquhavmgqtpkubrfzdm/editor');
    console.log('');
    console.log('📝 Execute este SQL:');
    console.log('');
    console.log(`
-- Criar tabela driver_profiles
CREATE TABLE IF NOT EXISTS driver_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    vehicle_type VARCHAR(50) DEFAULT 'economy',
    vehicle_make VARCHAR(100),
    vehicle_model VARCHAR(100),
    vehicle_year INTEGER,
    vehicle_color VARCHAR(50),
    vehicle_plate VARCHAR(20),
    license_number VARCHAR(50),
    license_expiry DATE,
    rating DECIMAL(3, 2) DEFAULT 4.5,
    total_rides INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Criar índices
CREATE INDEX IF NOT EXISTS idx_driver_profiles_user_id ON driver_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_driver_profiles_active ON driver_profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_driver_profiles_vehicle_type ON driver_profiles(vehicle_type);

-- Habilitar RLS
ALTER TABLE driver_profiles ENABLE ROW LEVEL SECURITY;

-- Criar políticas RLS
CREATE POLICY "Anyone can view active driver profiles" ON driver_profiles
    FOR SELECT USING (is_active = true);

CREATE POLICY "Drivers can insert their own profile" ON driver_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Drivers can update their own profile" ON driver_profiles
    FOR UPDATE USING (auth.uid() = user_id);

-- Conceder permissões
GRANT ALL ON driver_profiles TO authenticated;
    `);
    console.log('');
    console.log('⚠️ Após executar o SQL acima, execute este script novamente para verificar.');

    return false;

  } catch (error) {
    console.error('❌ Erro:', error.message);
    return false;
  }
}

// Função principal
async function createMissingTables() {
  console.log('🚀 Verificando e criando tabelas faltantes...');
  console.log(`📡 URL: ${supabaseUrl}`);

  try {
    // Verificar conexão
    const { data, error } = await supabase.from('profiles').select('count').limit(1);
    if (error) {
      throw new Error(`Erro de conexão: ${error.message}`);
    }
    console.log('✅ Conexão com Supabase estabelecida');

    // Verificar tabelas essenciais
    const tables = ['profiles', 'ride_requests', 'driver_locations', 'driver_profiles'];
    console.log('\n📋 Verificando tabelas...');

    for (const table of tables) {
      const exists = await tableExists(table);
      console.log(`  ${exists ? '✅' : '❌'} ${table}: ${exists ? 'existe' : 'não existe'}`);

      if (!exists && table === 'driver_profiles') {
        console.log('\n🔧 Tabela driver_profiles não encontrada. Tentando criar...');
        const created = await createDriverProfilesTable();

        if (!created) {
          console.log('\n❌ Não foi possível criar a tabela automaticamente.');
          console.log('📝 Por favor, execute o SQL fornecido acima no dashboard do Supabase.');
          return;
        }
      }
    }

    console.log('\n🎉 Verificação concluída!');

  } catch (error) {
    console.error('❌ Erro:', error.message);
    process.exit(1);
  }
}

// Executar
createMissingTables().catch(console.error);