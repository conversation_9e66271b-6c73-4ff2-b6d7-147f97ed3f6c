import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path' // Add this import

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 4005,
    host: true
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@shared': resolve(__dirname, '../shared')
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'esbuild',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom']
        }
      }
    }
  },
  // Define global constants
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
    // FORÇAR TOKEN MAPBOX HARDCODED PARA RESOLVER PROBLEMA DE VERCEL
    'import.meta.env.VITE_MAPBOX_ACCESS_TOKEN': JSON.stringify('pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g'),
    'process.env.VITE_MAPBOX_ACCESS_TOKEN': JSON.stringify('pk.eyJ1IjoiamVyZW1pYXMyMTA0MjEiLCJhIjoiY21hMG05ZThhMDBlMTJub2xubTl5N2t3dCJ9.KrCJ_WZ_79a92hFSISIq4g')
  }
})
