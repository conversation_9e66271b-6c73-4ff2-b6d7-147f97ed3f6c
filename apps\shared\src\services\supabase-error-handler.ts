/**
 * Sistema avançado para tratamento de erros do Supabase
 * Centraliza o tratamento de erros para melhorar a robustez da aplicação
 */

import { supabase } from './supabase-client';

// Tipos de erros do Supabase
export enum SupabaseErrorType {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  DATABASE = 'database',
  STORAGE = 'storage',
  REALTIME = 'realtime',
  FUNCTIONS = 'functions',
  UNKNOWN = 'unknown'
}

// Interface para erros do Supabase
export interface SupabaseError {
  type: SupabaseErrorType;
  code: string;
  message: string;
  originalError: any;
  timestamp: number;
  handled: boolean;
}

// Lista de códigos de erro conhecidos
const ERROR_CODES = {
  NETWORK: [
    'NETWORK_ERROR',
    'FETCH_ERROR',
    'CONNECTION_CLOSED',
    'CONNECTION_ERROR'
  ],
  AUTH: [
    'INVALID_CREDENTIALS',
    'TOKEN_EXPIRED',
    'TOKEN_REFRESH_FAILED',
    'USER_NOT_FOUND',
    'EMAIL_TAKEN',
    'UNAUTHORIZED',
    'INVALID_GRANT',
  ],
  DATABASE: [
    'PGRST',
    'POSTGRES_ERROR',
    'DB_ERROR',
    'FOREIGN_KEY_VIOLATION',
    'UNIQUE_VIOLATION',
    'CHECK_VIOLATION',
    'NOT_NULL_VIOLATION'
  ],
  REALTIME: [
    'REALTIME_ERROR',
    'SUBSCRIBE_ERROR',
    'CHANNEL_ERROR',
    'CHANNEL_CLOSED'
  ]
};

/**
 * Classe para gerenciar erros do Supabase
 */
class SupabaseErrorHandler {
  private static instance: SupabaseErrorHandler;
  private errors: SupabaseError[] = [];
  private maxErrors = 50;
  private listeners: ((error: SupabaseError) => void)[] = [];
  
  // Singleton pattern
  public static getInstance(): SupabaseErrorHandler {
    if (!SupabaseErrorHandler.instance) {
      SupabaseErrorHandler.instance = new SupabaseErrorHandler();
    }
    return SupabaseErrorHandler.instance;
  }
  
  private constructor() {
    this.setupGlobalErrorHandling();
  }
  
  /**
   * Configurar tratamento global de erros
   */
  private setupGlobalErrorHandling(): void {
    if (typeof window !== 'undefined') {
      // Interceptar erros de rede relacionados ao Supabase
      const originalFetch = window.fetch;
      window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
        try {
          const response = await originalFetch(input, init);
          
          // Verificar se é uma requisição do Supabase
          const url = typeof input === 'string' ? input : input.url;
          if (url.includes('supabase')) {
            if (!response.ok) {
              const error = new Error(`HTTP error ${response.status}: ${response.statusText}`);
              this.handleError(error, SupabaseErrorType.NETWORK, `HTTP_${response.status}`);
            }
          }
          
          return response;
        } catch (error) {
          // Verificar se é uma requisição do Supabase
          const url = typeof input === 'string' ? input : input.url;
          if (url.includes('supabase')) {
            this.handleError(error, SupabaseErrorType.NETWORK, 'FETCH_ERROR');
          }
          
          throw error;
        }
      };
      
      // Interceptar erros globais
      window.addEventListener('error', (event) => {
        if (event.message?.includes('supabase') || 
            event.filename?.includes('supabase') ||
            event.error?.stack?.includes('supabase')) {
          this.handleError(event.error, SupabaseErrorType.UNKNOWN);
        }
      });
      
      // Interceptar rejeições de promessas não tratadas
      window.addEventListener('unhandledrejection', (event) => {
        const error = event.reason;
        if (error?.message?.includes('supabase') || 
            error?.stack?.includes('supabase')) {
          this.handleError(error, SupabaseErrorType.UNKNOWN);
        }
      });
    }
  }
  
  /**
   * Detectar tipo de erro com base na mensagem ou código
   */
  private detectErrorType(error: any): SupabaseErrorType {
    const message = error?.message || '';
    const code = error?.code || '';
    
    // Verificar por códigos conhecidos
    if (ERROR_CODES.NETWORK.some(c => code.includes(c) || message.includes(c))) {
      return SupabaseErrorType.NETWORK;
    }
    
    if (ERROR_CODES.AUTH.some(c => code.includes(c) || message.includes(c))) {
      return SupabaseErrorType.AUTHENTICATION;
    }
    
    if (ERROR_CODES.DATABASE.some(c => code.includes(c) || message.includes(c))) {
      return SupabaseErrorType.DATABASE;
    }
    
    if (ERROR_CODES.REALTIME.some(c => code.includes(c) || message.includes(c))) {
      return SupabaseErrorType.REALTIME;
    }
    
    // Verificar por palavras-chave na mensagem
    if (message.includes('auth') || message.includes('login') || message.includes('session')) {
      return SupabaseErrorType.AUTHENTICATION;
    }
    
    if (message.includes('database') || message.includes('query') || message.includes('sql')) {
      return SupabaseErrorType.DATABASE;
    }
    
    if (message.includes('storage') || message.includes('bucket') || message.includes('file')) {
      return SupabaseErrorType.STORAGE;
    }
    
    if (message.includes('realtime') || message.includes('channel') || message.includes('subscribe')) {
      return SupabaseErrorType.REALTIME;
    }
    
    if (message.includes('function') || message.includes('edge') || message.includes('serverless')) {
      return SupabaseErrorType.FUNCTIONS;
    }
    
    return SupabaseErrorType.UNKNOWN;
  }
  
  /**
   * Obter código de erro
   */
  private getErrorCode(error: any): string {
    if (error.code) {
      return error.code;
    }
    
    if (error.statusCode) {
      return `HTTP_${error.statusCode}`;
    }
    
    if (error.status) {
      return `HTTP_${error.status}`;
    }
    
    return 'UNKNOWN_ERROR';
  }
  
  /**
   * Tratar erro do Supabase
   */
  public handleError(
    error: any, 
    type: SupabaseErrorType = SupabaseErrorType.UNKNOWN,
    code: string = ''
  ): SupabaseError {
    // Detectar tipo de erro se não fornecido
    if (type === SupabaseErrorType.UNKNOWN) {
      type = this.detectErrorType(error);
    }
    
    // Obter código de erro se não fornecido
    if (!code) {
      code = this.getErrorCode(error);
    }
    
    // Criar objeto de erro
    const supabaseError: SupabaseError = {
      type,
      code,
      message: error?.message || 'Erro desconhecido',
      originalError: error,
      timestamp: Date.now(),
      handled: false
    };
    
    // Adicionar à lista de erros
    this.errors.push(supabaseError);
    
    // Limitar tamanho da lista
    if (this.errors.length > this.maxErrors) {
      this.errors.shift();
    }
    
    // Notificar listeners
    this.notifyListeners(supabaseError);
    
    // Registrar no console
    console.error(`[Supabase Error] ${type}: ${supabaseError.message}`, error);
    
    // Tentar corrigir automaticamente
    this.autoFixError(supabaseError);
    
    return supabaseError;
  }
  
  /**
   * Tentar corrigir erro automaticamente
   */
  private autoFixError(error: SupabaseError): void {
    switch (error.type) {
      case SupabaseErrorType.NETWORK:
        // Verificar conexão de rede
        if (navigator.onLine) {
          console.log('🔄 Tentando reconectar após erro de rede...');
          
          // Tentar reconectar ao Supabase Realtime
          if (supabase.realtime) {
            setTimeout(() => {
              supabase.realtime.connect();
            }, 2000);
          }
        }
        break;
        
      case SupabaseErrorType.AUTHENTICATION:
        // Problemas de autenticação
        if (error.code === 'TOKEN_EXPIRED' || error.code === 'INVALID_CREDENTIALS') {
          console.log('🔑 Tentando atualizar token de autenticação...');
          
          // Tentar atualizar a sessão
          setTimeout(async () => {
            try {
              await supabase.auth.refreshSession();
              console.log('✅ Sessão atualizada com sucesso');
              error.handled = true;
            } catch (refreshError) {
              console.error('❌ Falha ao atualizar sessão:', refreshError);
            }
          }, 1000);
        }
        break;
        
      case SupabaseErrorType.REALTIME:
        // Problemas de realtime
        console.log('📡 Tentando reconectar ao Realtime após erro...');
        
        // Tentar reconectar ao Supabase Realtime
        if (supabase.realtime) {
          setTimeout(() => {
            try {
              supabase.realtime.connect();
              console.log('✅ Reconexão Realtime iniciada');
            } catch (reconnectError) {
              console.error('❌ Falha ao reconectar Realtime:', reconnectError);
            }
          }, 2000);
        }
        break;
        
      default:
        // Outros tipos de erro
        break;
    }
  }
  
  /**
   * Notificar listeners sobre novo erro
   */
  private notifyListeners(error: SupabaseError): void {
    this.listeners.forEach(listener => {
      try {
        listener(error);
      } catch (listenerError) {
        console.error('❌ Erro no listener de erro:', listenerError);
      }
    });
  }
  
  /**
   * Adicionar listener para erros
   */
  public addListener(listener: (error: SupabaseError) => void): () => void {
    this.listeners.push(listener);
    
    // Retornar função para remover listener
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }
  
  /**
   * Obter todos os erros
   */
  public getErrors(): SupabaseError[] {
    return [...this.errors];
  }
  
  /**
   * Obter erros por tipo
   */
  public getErrorsByType(type: SupabaseErrorType): SupabaseError[] {
    return this.errors.filter(error => error.type === type);
  }
  
  /**
   * Limpar todos os erros
   */
  public clearErrors(): void {
    this.errors = [];
  }
  
  /**
   * Marcar erro como tratado
   */
  public markAsHandled(errorId: number): boolean {
    const error = this.errors.find(e => e.timestamp === errorId);
    
    if (error) {
      error.handled = true;
      return true;
    }
    
    return false;
  }
  
  /**
   * Verificar se há erros críticos não tratados
   */
  public hasCriticalErrors(): boolean {
    return this.errors.some(e => 
      !e.handled && 
      (e.type === SupabaseErrorType.AUTHENTICATION || e.type === SupabaseErrorType.NETWORK)
    );
  }
}

// Exportar instância única
export const supabaseErrorHandler = SupabaseErrorHandler.getInstance();

// Função wrapper para operações do Supabase com tratamento de erros
export async function safeSupabaseOperation<T>(
  operation: () => Promise<T>,
  errorType: SupabaseErrorType = SupabaseErrorType.UNKNOWN
): Promise<{ data: T | null; error: SupabaseError | null }> {
  try {
    const result = await operation();
    return { data: result, error: null };
  } catch (error) {
    const supabaseError = supabaseErrorHandler.handleError(error, errorType);
    return { data: null, error: supabaseError };
  }
}

export default supabaseErrorHandler;