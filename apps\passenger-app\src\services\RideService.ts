import { supabase } from '../lib/supabase';
import { mapboxService } from './MapboxService';
import { simpleNotificationService } from './SimpleNotificationService';
import { analyticsService } from './AnalyticsService';

export interface RideRequest {
  id?: string;
  user_id: string;
  driver_id?: string;
  pickup_latitude: number;
  pickup_longitude: number;
  pickup_address: string;
  destination_latitude: number;
  destination_longitude: number;
  destination_address: string;
  estimated_distance?: number;
  estimated_duration?: number;
  estimated_price?: number;
  final_price?: number;
  status: 'pending' | 'searching' | 'accepted' | 'arrived' | 'in_progress' | 'completed' | 'cancelled';
  vehicle_type: string;
  payment_method: string;
  payment_status?: string;
  created_at?: string;
  updated_at?: string;
  accepted_at?: string;
  started_at?: string;
  completed_at?: string;
  cancelled_at?: string;
  cancellation_reason?: string;
  notes?: string;
}

export interface Driver {
  id: string;
  name: string;
  phone: string;
  rating: number;
  vehicle: {
    model: string;
    plate: string;
    color: string;
  };
  location: [number, number];
  distance: number;
  eta: number;
}

export class RideService {
  private static instance: RideService;

  static getInstance(): RideService {
    if (!RideService.instance) {
      RideService.instance = new RideService();
    }
    return RideService.instance;
  }

  // Criar uma nova solicitação de corrida (melhorado)
  async createRideRequest(
    originAddress: string,
    originCoords: [number, number],
    destinationAddress: string,
    destinationCoords: [number, number],
    vehicleType: string = 'economy',
    paymentMethod: string = 'cash'
  ): Promise<RideRequest | null> {
    try {
      // Obter usuário atual
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Usuário não autenticado');
      }

      // Calcular rota e preço com tipo de veículo
      const mapboxOriginCoords = [originCoords[1], originCoords[0]];
      const mapboxDestinationCoords = [destinationCoords[1], destinationCoords[0]];
      const estimate = await mapboxService.calculateRideEstimate(
        mapboxOriginCoords,
        mapboxDestinationCoords,
        vehicleType
      );
      if (!estimate) {
        throw new Error('Não foi possível calcular a rota');
      }

      // Criar dados da corrida com os campos disponíveis na tabela
      const rideData = {
        user_id: user.id,
        pickup_latitude: originCoords[0],
        pickup_longitude: originCoords[1],
        pickup_address: originAddress,
        destination_latitude: destinationCoords[0],
        destination_longitude: destinationCoords[1],
        destination_address: destinationAddress,
        vehicle_type: vehicleType,
        estimated_distance: estimate.distance / 1000,
        estimated_duration: Math.round(estimate.duration / 60),
        estimated_price: estimate.price,
        payment_method: paymentMethod,
        status: 'pending'
      };

      // Enhanced logging before inserting rideData
      console.log('Attempting to insert rideData:', JSON.stringify(rideData, null, 2));
      console.log('User ID for ride request:', user?.id);
      console.log('Raw originCoords:', JSON.stringify(originCoords, null, 2));
      console.log('Raw destinationCoords:', JSON.stringify(destinationCoords, null, 2));

      // Inserir a corrida no banco de dados
      const { data: createdRide, error: createError } = await supabase
        .from('ride_requests')
        .insert(rideData)
        .select('id')
        .single();

      if (createError) {
        console.error('Erro ao criar solicitação:', createError);
        return null;
      }

      const rideId = createdRide.id;

      // Criar objeto completo da corrida para retornar
      const completeRideRequest: RideRequest = {
        id: rideId,
        user_id: user.id,
        driver_id: null,
        pickup_latitude: originCoords[0],
        pickup_longitude: originCoords[1],
        pickup_address: originAddress,
        destination_latitude: destinationCoords[0],
        destination_longitude: destinationCoords[1],
        destination_address: destinationAddress,
        estimated_distance: estimate.distance / 1000,
        estimated_duration: Math.round(estimate.duration / 60),
        estimated_price: estimate.price,
        final_price: null,
        status: 'pending',
        vehicle_type: vehicleType,
        payment_method: paymentMethod,
        payment_status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        accepted_at: null,
        started_at: null,
        completed_at: null,
        cancelled_at: null
      };

      console.log('✅ Corrida criada com sucesso:', completeRideRequest);
      
      // Notificar motoristas próximos
      this.notifyNearbyDrivers(originCoords, rideId);

      // Registrar evento no analytics
      analyticsService.trackRideEvent('request_created', {
        ride_id: rideId,
        pickup_address: originAddress,
        destination_address: destinationAddress,
        estimated_distance: estimate.distance,
        estimated_price: estimate.price,
        vehicle_type: vehicleType,
        payment_method: paymentMethod,
        driver_assigned: false
      });

      // Notificar usuário sobre criação da solicitação
      await simpleNotificationService.createNotification(
        user.id,
        '🚗 Solicitação Criada',
        'Sua solicitação foi criada. Procurando motoristas próximos...',
        'ride_request',
        { ride_id: rideId },
        'normal'
      );

      return completeRideRequest;

    } catch (error) {
      console.error('Erro ao criar corrida:', error);
      return null;
    }
  }

  // Buscar motoristas próximos (simplificado)
  async findNearbyDrivers(userLocation: [number, number], radiusKm: number = 5): Promise<Driver[]> {
    try {
      // Buscar motoristas disponíveis (sem cálculo de distância por enquanto)
      const { data, error } = await supabase
        .from('driver_locations')
        .select(`
          *,
          driver_profiles!inner(
            id,
            full_name,
            phone,
            vehicle_type,
            rating
          )
        `)
        .eq('is_available', true)
        .eq('is_active', true)
        .limit(10);

      if (error) {
        console.error('Erro ao buscar motoristas:', error);
        return [];
      }

      if (!data || data.length === 0) {
        console.log('Nenhum motorista encontrado na região');
        return [];
      }

      return data.map((driver: any) => ({
        id: driver.driver_profiles.id,
        name: driver.driver_profiles.full_name || 'Motorista',
        phone: driver.driver_profiles.phone || '',
        rating: driver.driver_profiles.rating || 4.5,
        vehicle: {
          model: driver.driver_profiles.vehicle_type === 'moto' ? 'Honda CG 160' : 'Honda Civic',
          plate: 'ABC-1234', // TODO: Adicionar placa real do banco
          color: 'Prata',
          type: driver.driver_profiles.vehicle_type || 'economy'
        },
        location: [driver.longitude, driver.latitude],
        distance: 2.5, // Mock distance
        eta: 5, // Mock ETA
        heading: driver.heading || 0,
        speed: driver.speed || 0,
        isAvailable: driver.is_available
      }));

    } catch (error) {
      console.error('Erro ao buscar motoristas:', error);
      return [];
    }
  }

  // Notificar motoristas próximos sobre nova corrida
  private async notifyNearbyDrivers(location: [number, number], rideId: string) {
    try {
      const drivers = await this.findNearbyDrivers(location);
      
      // Enviar notificação para cada motorista
      for (const driver of drivers) {
        await this.sendDriverNotification(driver.id, rideId);
      }

    } catch (error) {
      console.error('Erro ao notificar motoristas:', error);
    }
  }

  // Enviar notificação para motorista
  private async sendDriverNotification(driverId: string, rideId: string) {
    try {
      await supabase
        .from('notifications')
        .insert({
          user_id: driverId,
          title: 'Nova Corrida Disponível',
          message: 'Uma nova corrida está disponível próxima a você',
          type: 'ride_request',
          data: { ride_id: rideId }
        });

    } catch (error) {
      console.error('Erro ao enviar notificação:', error);
    }
  }

  // Obter status da corrida
  async getRideStatus(rideId: string): Promise<RideRequest | null> {
    try {
      const { data, error } = await supabase
        .from('ride_requests')
        .select(`
          *,
          driver:profiles!ride_requests_driver_id_fkey(
            id,
            full_name,
            phone
          )
        `)
        .eq('id', rideId)
        .single();

      if (error) {
        console.error('Erro ao obter status:', error);
        return null;
      }

      return data;

    } catch (error) {
      console.error('Erro ao obter status da corrida:', error);
      return null;
    }
  }

  // Cancelar corrida
  async cancelRide(rideId: string, reason?: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('ride_requests')
        .update({
          status: 'cancelled',
          cancelled_at: new Date().toISOString()
        })
        .eq('id', rideId);

      if (error) {
        console.error('Erro ao cancelar corrida:', error);
        return false;
      }

      return true;

    } catch (error) {
      console.error('Erro ao cancelar corrida:', error);
      return false;
    }
  }

  // Obter histórico de corridas do usuário
  async getUserRideHistory(limit: number = 10): Promise<RideRequest[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return [];

      const { data, error } = await supabase
        .from('ride_requests')
        .select(`
          *,
          driver:profiles!ride_requests_driver_id_fkey(
            id,
            full_name,
            phone
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Erro ao obter histórico:', error);
        return [];
      }

      return data || [];

    } catch (error) {
      console.error('Erro ao obter histórico de corridas:', error);
      return [];
    }
  }

  // Avaliar corrida
  async rateRide(rideId: string, rating: number, comment?: string): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return false;

      // Obter dados da corrida
      const ride = await this.getRideStatus(rideId);
      if (!ride || !ride.driver_id) return false;

      // Inserir avaliação
      const { error } = await supabase
        .from('ride_ratings')
        .insert({
          ride_id: rideId,
          rater_id: user.id,
          rated_id: ride.driver_id,
          rating,
          comment
        });

      if (error) {
        console.error('Erro ao avaliar corrida:', error);
        return false;
      }

      return true;

    } catch (error) {
      console.error('Erro ao avaliar corrida:', error);
      return false;
    }
  }

  // Calcular preço estimado
  calculateEstimatedPrice(distance: number, duration: number, vehicleType: string = 'economy'): number {
    const basePrices = {
      economy: { base: 5.00, perKm: 2.50, perMin: 0.30 },
      comfort: { base: 8.00, perKm: 3.50, perMin: 0.40 },
      premium: { base: 12.00, perKm: 5.00, perMin: 0.60 },
      moto: { base: 3.00, perKm: 1.80, perMin: 0.20 }
    };

    const pricing = basePrices[vehicleType as keyof typeof basePrices] || basePrices.economy;
    const distanceKm = distance / 1000;
    const durationMin = duration / 60;
    
    const price = pricing.base + (distanceKm * pricing.perKm) + (durationMin * pricing.perMin);
    
    // Preço mínimo de R$ 5,00
    return Math.max(price, 5.00);
  }

  // Subscrever a atualizações de corrida em tempo real (habilitado)
  subscribeToRideUpdates(rideId: string, callback: (ride: RideRequest) => void) {
    console.log('🔄 Iniciando subscription para ride updates:', rideId);

    try {
      const channel = supabase
        .channel(`ride-updates-${rideId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'ride_requests',
            filter: `id=eq.${rideId}`
          },
          (payload) => {
            console.log('📡 Ride update recebido:', payload);

            if (payload.new) {
              const rideData = payload.new as any;

              // Converter para formato esperado
              const rideUpdate: RideRequest = {
                id: rideData.id,
                user_id: rideData.user_id,
                driver_id: rideData.driver_id,
                origin_address: rideData.origin_address,
                origin_coords: [
                  rideData.origin_coords?.x || rideData.origin_coords?.coordinates?.[0] || 0,
                  rideData.origin_coords?.y || rideData.origin_coords?.coordinates?.[1] || 0
                ],
                destination_address: rideData.destination_address,
                destination_coords: [
                  rideData.destination_coords?.x || rideData.destination_coords?.coordinates?.[0] || 0,
                  rideData.destination_coords?.y || rideData.destination_coords?.coordinates?.[1] || 0
                ],
                distance: rideData.distance,
                duration: rideData.duration,
                estimated_price: rideData.estimated_price,
                final_price: rideData.final_price,
                status: rideData.status,
                vehicle_type: rideData.vehicle_type,
                payment_method: rideData.payment_method,
                created_at: rideData.created_at,
                accepted_at: rideData.accepted_at,
                started_at: rideData.started_at,
                completed_at: rideData.completed_at,
                cancelled_at: rideData.cancelled_at
              };

              callback(rideUpdate);
            }
          }
        )
        .subscribe();

      return {
        unsubscribe: () => {
          console.log('🛑 Unsubscribing from ride updates:', rideId);
          supabase.removeChannel(channel);
        }
      };

    } catch (error) {
      console.error('❌ Erro ao criar subscription:', error);
      return {
        unsubscribe: () => console.log('Mock unsubscribe due to error')
      };
    }
  }
}

export const rideService = RideService.getInstance();
