import { createClient } from '@supabase/supabase-js';

// Detectar ambiente
const isBrowser = typeof window !== 'undefined';
const isDevelopment = (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') ||
                     (typeof import.meta !== 'undefined' && (import.meta as any).env?.MODE === 'development');

// Função para obter variáveis de ambiente de forma segura
const getEnvVar = (key: string): string | undefined => {
  // Vite / Browser
  if (typeof import.meta !== 'undefined' && (import.meta as any).env) {
    return (import.meta as any).env[key];
  }

  // Node.js
  if (typeof process !== 'undefined' && process.env) {
    return process.env[key];
  }

  return undefined;
};

// Configuração do Supabase - SEM HARDCODED CREDENTIALS
const supabaseUrl = getEnvVar('VITE_SUPABASE_URL') || getEnvVar('NEXT_PUBLIC_SUPABASE_URL');
const supabaseAnonKey = getEnvVar('VITE_SUPABASE_ANON_KEY') || getEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY');

// Verificar se as variáveis estão configuradas
if (!supabaseUrl || !supabaseAnonKey) {
  const errorMsg = '❌ Variáveis de ambiente do Supabase não configuradas!\n' +
                   'Configure: VITE_SUPABASE_URL e VITE_SUPABASE_ANON_KEY\n' +
                   'ou: NEXT_PUBLIC_SUPABASE_URL e NEXT_PUBLIC_SUPABASE_ANON_KEY';

  if (isDevelopment) {
    console.error(errorMsg);
  }
  throw new Error('Supabase configuration missing');
}

// Tabelas padronizadas do banco de dados
export const TABLES = {
  PROFILES: 'profiles',
  DRIVERS: 'drivers',
  RIDE_REQUESTS: 'ride_requests',
  DRIVER_LOCATIONS: 'driver_locations',
  PAYMENTS: 'payments',
  PAYMENT_METHODS: 'payment_methods',
  CHAT_MESSAGES: 'chat_messages',
  NOTIFICATIONS: 'notifications',
  APP_SETTINGS: 'app_settings',
} as const;

// Buckets de armazenamento
export const STORAGE_BUCKETS = {
  PROFILE_IMAGES: 'profile_images',
  DOCUMENTS: 'documents',
  RECORDINGS: 'recordings',
} as const;

// Configuração simplificada e limpa do Supabase
const supabaseConfig = {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
    storage: isBrowser ? localStorage : undefined,
    storageKey: 'mobdrive-auth-token',
    flowType: 'pkce' as const,
  },
  global: {
    headers: {
      'X-Client-Info': 'MobiDrive/2.0.0',
    },
  },
  realtime: {
    autoconnect: false, // Conectar apenas quando necessário
  },
  db: {
    schema: 'public',
  },
};

// Criar cliente Supabase
export const supabase = createClient(supabaseUrl, supabaseAnonKey, supabaseConfig);

// Log de inicialização
if (isDevelopment) {
  console.log('✅ Supabase client inicializado');
}

// Métodos auxiliares simplificados
export const supabaseHelpers = {
  /**
   * Verifica se o Supabase está conectado e acessível
   */
  async checkConnection(): Promise<{ connected: boolean, latency: number }> {
    const start = Date.now();
    try {
      const { error } = await supabase.from(TABLES.RIDE_REQUESTS).select('count').limit(1);
      const latency = Date.now() - start;
      return { connected: !error, latency };
    } catch (error) {
      const latency = Date.now() - start;
      return { connected: false, latency };
    }
  },

  /**
   * Obtém o usuário atual
   */
  async getCurrentUser() {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      return { success: !error, user, error };
    } catch (error) {
      return { success: false, user: null, error };
    }
  },

  /**
   * Executa uma operação com retry simples
   */
  async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3
  ): Promise<T> {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    throw lastError;
  },

  /**
   * Verifica saúde geral do sistema
   */
  async getSystemHealth() {
    const connection = await this.checkConnection();
    const user = await this.getCurrentUser();

    return {
      timestamp: new Date().toISOString(),
      connection,
      auth: user,
      overall: connection.connected ? 'healthy' : 'unhealthy'
    };
  }
};

// Verificar conexão inicial em desenvolvimento
if (isDevelopment && isBrowser) {
  supabaseHelpers.checkConnection().then(({connected, latency}) => {
    if (connected) {
      console.log(`✅ Conexão inicial com Supabase: OK (${latency}ms)`);
    } else {
      console.warn('⚠️ Verificar configuração do Supabase - Conexão inicial falhou');
    }
  });

  // Expor cliente para debugging
  (window as any).__supabase = supabase;
}

export default supabase;