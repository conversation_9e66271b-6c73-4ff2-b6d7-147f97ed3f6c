import { createClient } from '@supabase/supabase-js';

// Configurações e ambientes
const ENV = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test'
};

// Detectar ambiente atual
const getCurrentEnv = (): string => {
  if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV) {
    return process.env.NODE_ENV;
  }
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    return import.meta.env.MODE || ENV.DEVELOPMENT;
  }
  return ENV.DEVELOPMENT;
};

// Variáveis de ambiente com fallbacks
const getEnv = (key: string, fallback: string): string => {
  // Vite / Browser
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    return import.meta.env[key] || fallback;
  }
  
  // Node.js
  if (typeof process !== 'undefined' && process.env) {
    return process.env[key] || fallback;
  }
  
  return fallback;
};

// URL e chave do Supabase com fallbacks
const supabaseUrl = getEnv('VITE_SUPABASE_URL', 'https://udquhavmgqtpkubrfzdm.supabase.co');
const supabaseAnonKey = getEnv('VITE_SUPABASE_ANON_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVkcXVoYXZtZ3F0cGt1YnJmemRtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxNjAzNjMsImV4cCI6MjA1NDczNjM2M30.Y-C1cZJ8785JuGm5RilLBeiIB78gySlnk-h6wRfg3cI');

// Detectar browser
const isBrowser = typeof window !== 'undefined';

// Tabelas do banco de dados
export const TABLES = {
  profiles: 'profiles',
  drivers: 'drivers',
  ride_requests: 'ride_requests',
  driver_locations: 'driver_locations',
  user_locations: 'user_locations',
  payments: 'payments',
  payment_methods: 'payment_methods',
  chat_messages: 'chat_messages',
  notifications: 'notifications',
  app_settings: 'app_settings',
};

// Configuração de armazenamento
export const STORAGE_BUCKETS = {
  profileImages: 'profile_images',
  documents: 'documents',
  recordings: 'recordings',
};

// Configuração de autenticação otimizada
const authConfig = {
  autoRefreshToken: true,
  persistSession: true,
  detectSessionInUrl: false,
  storage: isBrowser ? localStorage : undefined,
  // Chaves diferentes para cada app evita conflitos de sessão
  storageKey: 'mobidrive-auth-token',
  flowType: 'pkce',
};

// Configurações avançadas para o cliente Supabase
const supabaseConfig = {
  auth: authConfig,
  global: {
    headers: {
      'X-Client-Info': 'MobiDrive/1.1.0',
      'Pragma': 'no-cache',
      'Cache-Control': 'no-cache',
    },
    // Adiciona timestamp para prevenir cache do browser
    fetch: isBrowser ? (url: string, options: any) => {
      // Adicionar timestamp para requisições GET
      if (options?.method === 'GET') {
        url = url + (url.includes('?') ? '&' : '?') + `_t=${Date.now()}`;
      }
      return fetch(url, options);
    } : undefined,
  },
  realtime: {
    // Conectar automaticamente - CRUCIAL para ambiente de produção
    autoconnect: true,
    // Timeout para cancelar inscrições inativas
    timeout: 60000,
    // Detectar desconexão
    params: {
      heartbeat: true
    }
  },
  db: {
    schema: 'public',
  },
};

// Criar cliente Supabase
export const supabase = createClient(supabaseUrl, supabaseAnonKey, supabaseConfig);

// Adicionar funcionalidades de telemetria e reconexão
if (isBrowser) {
  // Interceptar e medir performance das requisições
  const originalRealtime = supabase.realtime;
  
  // Tratamento de erros avançado para funções realtime
  if (originalRealtime) {
    const originalConnect = originalRealtime.connect.bind(originalRealtime);
    originalRealtime.connect = async () => {
      try {
        console.log('🔄 Conectando ao Supabase Realtime...');
        const result = await originalConnect();
        console.log('✅ Conexão Realtime estabelecida com sucesso');
        return result;
      } catch (error) {
        console.error('❌ Falha na conexão Realtime:', error);
        // Tentar novamente após 3 segundos - crucial para manter sincronização
        setTimeout(() => {
          console.log('🔄 Tentando reconectar ao Realtime...');
          originalConnect().catch(e => {
            console.error('❌ Reconexão falhou:', e);
            // Tentar novamente com intervalo maior
            setTimeout(() => originalConnect(), 5000);
          });
        }, 2000);
        throw error;
      }
    };
  }

  // Monitor de falhas
  window.addEventListener('error', (event) => {
    if (event.message?.includes('supabase') || event.filename?.includes('supabase')) {
      console.error('❌ Erro relacionado ao Supabase detectado:', event);
      // Tentar reconectar em caso de erro crítico
      if (supabase.realtime && typeof supabase.realtime.connect === 'function') {
        setTimeout(() => supabase.realtime.connect(), 2000);
      }
    }
  });

  // Monitor de conexão - CRUCIAL para ambiente de produção
  window.addEventListener('online', () => {
    console.log('🌐 Conexão de internet restaurada. Reconectando Supabase...');
    if (supabase.realtime) {
      supabase.realtime.connect();
    }
    
    // Verificar conexão com o banco após restaurar internet
    supabaseHelpers.checkConnection().then(({connected}) => {
      if (!connected) {
        console.warn('⚠️ Conexão Supabase não restaurada completamente. Tentando novamente...');
        setTimeout(() => supabase.realtime.connect(), 1000);
      } else {
        console.log('✅ Conexão com Supabase restaurada com sucesso!');
      }
    });
  });

  window.addEventListener('offline', () => {
    console.log('🔌 Conexão de internet perdida. Tentando manter conexão Supabase...');
  });
  
  // Verificar conexão a cada 30 segundos (importante para produção)
  setInterval(() => {
    if (navigator.onLine) {
      supabaseHelpers.checkConnection().then(({connected, latency}) => {
        if (!connected && supabase.realtime) {
          console.warn('⚠️ Conexão com Supabase perdida. Tentando reconectar...');
          supabase.realtime.connect();
        } else {
          console.log(`✅ Verificação de conexão: ${connected ? 'OK' : 'Falha'} (${latency}ms)`);
        }
      });
    }
  }, 30000);
}

// Métodos auxiliares
export const supabaseHelpers = {
  /**
   * Verifica se o Supabase está conectado e acessível
   */
  async checkConnection(): Promise<{ connected: boolean, latency: number }> {
    const start = Date.now();
    try {
      const { error } = await supabase.from(TABLES.app_settings).select('count').limit(1);
      const latency = Date.now() - start;
      return { connected: !error, latency };
    } catch (error) {
      const latency = Date.now() - start;
      return { connected: false, latency };
    }
  },

  /**
   * Limpa o cache do Supabase apenas para dados não críticos
   */
  clearCache(): void {
    if (!isBrowser) return;

    // NÃO limpar tokens de autenticação para não desconectar usuários
    // Limpar apenas caches temporários e não essenciais
    Object.keys(localStorage).forEach(key => {
      if ((key.includes('supabase') || key.includes('mobidrive')) && 
          !key.includes('auth') && 
          !key.includes('token')) {
        localStorage.removeItem(key);
      }
    });

    console.log('🧹 Cache não essencial do Supabase limpo');
  },

  /**
   * Executa uma operação com retry automático
   * Crucial para garantir operações em produção
   */
  async withRetry<T>(
    operation: () => Promise<T>,
    options: { maxRetries?: number, delayMs?: number, criticalOperation?: boolean } = {}
  ): Promise<T> {
    const { maxRetries = 3, delayMs = 1000, criticalOperation = false } = options;
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        console.warn(`⚠️ Tentativa ${attempt} falhou, tentando novamente em ${delayMs}ms...`);
        
        // Esperar antes de tentar novamente
        await new Promise(resolve => setTimeout(resolve, delayMs * attempt)); // Backoff exponencial
        
        // Verificar conexão antes de nova tentativa
        if (criticalOperation) {
          const {connected} = await supabaseHelpers.checkConnection();
          if (!connected) {
            console.error('❌ Servidor Supabase não está acessível! Tentando novamente...');
          }
        }
      }
    }
    
    // Para operações críticas, tenta mais uma vez após um intervalo maior
    if (criticalOperation) {
      try {
        console.log('🔄 Última tentativa para operação crítica após pausa...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        return await operation();
      } catch (finalError) {
        console.error('❌ Todas as tentativas falharam para operação crítica!', finalError);
        throw finalError;
      }
    }
    
    throw lastError;
  },
  
  /**
   * Sincroniza dados imediatamente e garante que foram enviados
   */
  async syncImmediately(
    table: string,
    data: any,
    options: { upsert?: boolean, onConflict?: string } = {}
  ): Promise<{success: boolean, data: any, error: any}> {
    const { upsert = false, onConflict } = options;
    
    // Configurar a operação com base nos parâmetros
    const operation = upsert
      ? supabase.from(table).upsert(data, { onConflict, returning: 'minimal' })
      : supabase.from(table).insert(data, { returning: 'minimal' });
    
    // Executar com retry para garantir sincronização
    try {
      const result = await this.withRetry(
        async () => operation,
        { maxRetries: 5, criticalOperation: true }
      );
      
      return { success: !result.error, data: result.data, error: result.error };
    } catch (error) {
      console.error(`❌ Falha ao sincronizar dados para ${table}:`, error);
      return { success: false, data: null, error };
    }
  }
};

// Status da conexão para monitoramento
export const connectionStatus = {
  isConnected: true,
  lastSuccess: Date.now(),
  errors: [] as string[],
  retries: 0,
  
  // Registrar falha de conexão
  registerError(message: string) {
    this.errors.push(`${new Date().toISOString()}: ${message}`);
    if (this.errors.length > 10) this.errors.shift();
    this.retries++;
  },
  
  // Registrar sucesso
  registerSuccess() {
    this.isConnected = true;
    this.lastSuccess = Date.now();
    this.retries = 0;
  }
};

// Verificar conexão inicial
if (isBrowser) {
  supabaseHelpers.checkConnection().then(({connected}) => {
    if (connected) {
      console.log('✅ Conexão inicial com Supabase: OK');
      connectionStatus.registerSuccess();
    } else {
      console.warn('⚠️ Verificar configuração do Supabase - Conexão inicial falhou');
      connectionStatus.registerError('Falha na conexão inicial');
    }
  });
}

// Expor cliente para debugging apenas em desenvolvimento
if (getCurrentEnv() === ENV.DEVELOPMENT && isBrowser) {
  (window as any).__supabase = supabase;
}

export default supabase;