import React, { useRef, useState, useEffect, Suspense } from 'react'
import { Canvas, useFrame, useLoader } from '@react-three/fiber'
import { Environment } from '@react-three/drei'
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';
import * as THREE from 'three'

// Componente que carrega o modelo .obj com debug
const DebugCarModel: React.FC<{ manualRotation: [number, number, number] }> = ({ manualRotation }) => {
  const carRef = useRef<THREE.Group>(null)

  // Carregar o modelo .obj
  console.log('🚗 Iniciando carregamento do modelo .obj...')
  const carModel = useLoader(OBJLoader, '/gif/1ef11134-45b5-44ca-af9b-557603c5e881/Pbr/base.obj')

  console.log('✅ Modelo .obj carregado com sucesso!')
  console.log('📊 Informações do modelo:', {
    children: carModel.children.length,
    type: carModel.type,
    position: carModel.position,
    scale: carModel.scale,
    rotation: carModel.rotation
  })

  useFrame((state) => {
    if (carRef.current) {
      // Rotação baseada no scroll da página
      const scrollY = window.scrollY || 0
      const maxScroll = Math.max(document.body.scrollHeight - window.innerHeight, 1)
      const scrollProgress = Math.min(scrollY / maxScroll, 1)

      // Combinar rotação do scroll + rotação manual + rotação automática lenta
      carRef.current.rotation.x = manualRotation[0] + Math.sin(scrollProgress * Math.PI) * 0.1
      carRef.current.rotation.y = manualRotation[1] + scrollProgress * Math.PI * 2 + state.clock.elapsedTime * 0.02
      carRef.current.rotation.z = manualRotation[2]

      // Movimento sutil para cima e para baixo
      carRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  // Aplicar materiais básicos ao modelo
  useEffect(() => {
    if (carModel) {
      console.log('🎨 Aplicando materiais ao modelo...')
      let meshCount = 0

      carModel.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          meshCount++

          // Criar material básico
          const material = new THREE.MeshStandardMaterial({
            color: '#1a1a1a',
            metalness: 0.8,
            roughness: 0.2,
          })

          child.material = material
          child.castShadow = true
          child.receiveShadow = true
        }
      })

      console.log(`✅ Materiais aplicados a ${meshCount} meshes`)
    }
  }, [carModel])

  return (
    <group ref={carRef} position={[0, 0, 0]} scale={[3, 3, 3]}>
      {carModel && <primitive object={carModel} />}
    </group>
  )
}

// Componente de fallback
const FallbackCar: React.FC<{ manualRotation: [number, number, number] }> = ({ manualRotation }) => {
  const carRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (carRef.current) {
      const scrollY = window.scrollY || 0
      const maxScroll = Math.max(document.body.scrollHeight - window.innerHeight, 1)
      const scrollProgress = Math.min(scrollY / maxScroll, 1)

      carRef.current.rotation.x = manualRotation[0] + Math.sin(scrollProgress * Math.PI) * 0.1
      carRef.current.rotation.y = manualRotation[1] + scrollProgress * Math.PI * 2 + state.clock.elapsedTime * 0.02
      carRef.current.rotation.z = manualRotation[2]
      carRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  return (
    <group ref={carRef} position={[0, 0, 0]} scale={[1.2, 1.2, 1.2]}>
      {/* Corpo principal do carro */}
      <mesh position={[0, 0.5, 0]} castShadow receiveShadow>
        <boxGeometry args={[4.5, 1.2, 2.2]} />
        <meshStandardMaterial
          color="#0a0a0a"
          metalness={0.95}
          roughness={0.05}
          envMapIntensity={1.5}
        />
      </mesh>

      {/* Teto do carro */}
      <mesh position={[0.2, 1.4, 0]} castShadow>
        <boxGeometry args={[3.2, 0.9, 2]} />
        <meshStandardMaterial
          color="#1a1a1a"
          metalness={0.9}
          roughness={0.1}
        />
      </mesh>

      {/* Rodas */}
      {[
        [1.6, -0.3, 1.3],
        [1.6, -0.3, -1.3],
        [-1.6, -0.3, 1.3],
        [-1.6, -0.3, -1.3]
      ].map((position, index) => (
        <group key={index} position={position as [number, number, number]}>
          <mesh rotation={[Math.PI / 2, 0, 0]} castShadow>
            <cylinderGeometry args={[0.5, 0.5, 0.4]} />
            <meshStandardMaterial color="#1a1a1a" metalness={0.1} roughness={0.9} />
          </mesh>
          <mesh rotation={[Math.PI / 2, 0, 0]} position={[0, 0, 0.15]} castShadow>
            <cylinderGeometry args={[0.35, 0.35, 0.1]} />
            <meshStandardMaterial color="#c0c0c0" metalness={0.9} roughness={0.1} />
          </mesh>
        </group>
      ))}

      {/* Faróis */}
      <mesh position={[2.3, 0.4, 0.8]} castShadow>
        <sphereGeometry args={[0.2]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={0.5}
        />
      </mesh>
      <mesh position={[2.3, 0.4, -0.8]} castShadow>
        <sphereGeometry args={[0.2]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={0.5}
        />
      </mesh>
    </group>
  )
}

// Componente de loading
const LoadingIndicator: React.FC = () => {
  const loaderRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (loaderRef.current) {
      loaderRef.current.rotation.y = state.clock.elapsedTime * 1
    }
  })

  return (
    <group ref={loaderRef}>
      <mesh position={[0, 0, 0]}>
        <torusGeometry args={[1, 0.2, 8, 16]} />
        <meshStandardMaterial
          color="#3B82F6"
          emissive="#3B82F6"
          emissiveIntensity={0.5}
        />
      </mesh>

      <mesh position={[0, 0.5, 0]}>
        <sphereGeometry args={[0.1]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={1}
        />
      </mesh>
    </group>
  )
}

// Componente principal
interface Car3DDebugProps {
  className?: string
  useRealModel?: boolean
}

export const Car3DDebug: React.FC<Car3DDebugProps> = ({
  className = "",
  useRealModel = true
}) => {
  const [rotation, setRotation] = useState<[number, number, number]>([0, 0, 0])
  const [isDragging, setIsDragging] = useState(false)
  const [lastPosition, setLastPosition] = useState({ x: 0, y: 0 })
  const [modelError, setModelError] = useState(false)

  // Handlers para mouse
  const handleMouseDown = (event: React.MouseEvent) => {
    event.preventDefault()
    setIsDragging(true)
    setLastPosition({ x: event.clientX, y: event.clientY })
  }

  const handleMouseMove = (event: React.MouseEvent) => {
    if (!isDragging) return
    event.preventDefault()

    const deltaX = event.clientX - lastPosition.x
    const deltaY = event.clientY - lastPosition.y

    setRotation(prev => [
      prev[0] + deltaY * 0.005,
      prev[1] + deltaX * 0.005,
      prev[2]
    ])

    setLastPosition({ x: event.clientX, y: event.clientY })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  // Event listeners globais
  useEffect(() => {
    const handleGlobalMouseUp = () => setIsDragging(false)
    const handleGlobalMouseMove = (event: MouseEvent) => {
      if (!isDragging) return

      const deltaX = event.clientX - lastPosition.x
      const deltaY = event.clientY - lastPosition.y

      setRotation(prev => [
        prev[0] + deltaY * 0.005,
        prev[1] + deltaX * 0.005,
        prev[2]
      ])

      setLastPosition({ x: event.clientX, y: event.clientY })
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove)
      document.addEventListener('mouseup', handleGlobalMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
    }
  }, [isDragging, lastPosition])

  return (
    <div
      className={`w-full h-full ${className} select-none relative`}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1,
        pointerEvents: 'auto',
        cursor: isDragging ? 'grabbing' : 'grab',
        touchAction: 'none'
      }}
    >
      <Canvas
        shadows
        camera={{ position: [6, 4, 6], fov: 50 }}
        style={{
          background: 'transparent',
          width: '100%',
          height: '100%'
        }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
        dpr={[1, 2]}
        onError={(error) => {
          console.error('❌ Erro no Canvas:', error)
          setModelError(true)
        }}
      >
        {/* Iluminação */}
        <ambientLight intensity={0.4} />
        <directionalLight
          position={[10, 10, 5]}
          intensity={1.2}
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
        />
        <pointLight position={[-5, 5, -5]} intensity={0.4} />
        <pointLight position={[5, 5, 5]} intensity={0.4} />

        {/* Ambiente */}
        <Environment preset="city" />

        {/* Carro com Suspense */}
        <Suspense fallback={<LoadingIndicator />}>
          {useRealModel && !modelError ? (
            <DebugCarModel manualRotation={rotation} />
          ) : (
            <FallbackCar manualRotation={rotation} />
          )}
        </Suspense>

        {/* Chão */}
        <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1, 0]} receiveShadow>
          <planeGeometry args={[30, 30]} />
          <meshStandardMaterial
            color="#1a1a1a"
            transparent
            opacity={0.2}
            metalness={0.8}
            roughness={0.2}
          />
        </mesh>
      </Canvas>

      {/* Debug info */}
      <div className="absolute top-4 left-4 bg-black/50 text-white p-3 rounded text-sm max-w-xs">
        <div className="font-bold mb-2">🚗 Debug do Modelo 3D</div>
        <div>Modelo Real: {useRealModel ? 'Sim' : 'Não'}</div>
        <div>Erro: {modelError ? 'Sim' : 'Não'}</div>
        <div>Status: {modelError ? 'Fallback ativo' : 'Carregando/Ativo'}</div>
        <div className="mt-2 text-xs opacity-75">
          Arraste para rotacionar
        </div>
      </div>
    </div>
  )
}

export default Car3DDebug
