import { supabase, TABLES } from '../../shared/src/services/supabase-client';
import { realtimeManager } from '../../shared/src/services/supabase-realtime-manager';
import { supabaseErrorHandler } from '../../shared/src/services/supabase-error-handler';
import { syncService } from '../../shared/src/services/supabase-sync-service';

// Configuração específica do app de passageiro
const APP_CONFIG = {
  appType: 'passenger',
  appVersion: '1.1.0',
  realtime: {
    autoconnect: true, // ✅ HABILITADO: Realtime ativo para corridas
  }
};
// Serviço de localização em tempo real (simplificado para evitar stack overflow)
export class RealtimeLocationService {
  private isTracking = false;
  private pollInterval: NodeJS.Timeout | null = null;
  private subscriptions: Map<string, any> = new Map();

  // Iniciar tracking de motoristas próximos
  startDriverTracking(userLocation: { lat: number; lng: number }, callback: (drivers: any[]) => void) {
    try {
      console.log('🚗 Iniciando tracking de motoristas em tempo real');

      // Parar tracking anterior se existir
      this.stopDriverTracking();

      this.isTracking = true;

      // Buscar motoristas iniciais
      this.fetchNearbyDrivers(userLocation, callback);

      // Configurar polling como fallback a cada 15 segundos
      this.pollInterval = setInterval(() => {
        if (this.isTracking) {
          this.fetchNearbyDrivers(userLocation, callback);
        }
      }, 15000);

      // Inscrever-se para atualizações em tempo real
      const subscription = realtimeManager.subscribe(
        'driver-locations',
        'postgres_changes',
        '*',
        (payload) => {
          // Processar alteração de localização
          if (payload.new) {
            callback([payload.new]);
          }
        },
        { criticalChannel: true }
      );

      this.subscriptions.set('driver-locations', subscription);

      console.log('✅ Tracking de motoristas ativo (modo realtime com fallback polling)');
    } catch (error) {
      console.error('Erro ao iniciar tracking:', error);
      // Fallback: buscar motoristas apenas com polling
      this.fetchNearbyDrivers(userLocation, callback);
    }
  }

  // Parar tracking com cleanup seguro
  stopDriverTracking() {
    try {
      if (this.pollInterval) {
        clearInterval(this.pollInterval);
        this.pollInterval = null;
      }

      // Cancelar inscrições
      for (const [key, subscription] of this.subscriptions.entries()) {
        try {
          realtimeManager.unsubscribe(`mobidrive-${key}`);
        } catch (error) {
          console.warn(`⚠️ Erro ao cancelar subscription ${key}:`, error);
        }
      }

      this.subscriptions.clear();
      this.isTracking = false;

      console.log('🛑 Tracking de motoristas parado');
    } catch (error) {
      console.error('Erro ao parar tracking:', error);
      // Force cleanup
      this.pollInterval = null;
      this.isTracking = false;
    }
  }

  // Buscar motoristas próximos (usando query simples)
  private async fetchNearbyDrivers(userLocation: { lat: number; lng: number }, callback: (drivers: any[]) => void) {
    try {
      // Usar query simples
      const { data: drivers, error } = await supabase
        .from('driver_locations')
        .select('*')
        .eq('is_available', true)
        .limit(10);

      if (error) {
        console.error('Erro ao buscar motoristas:', error);
        // Registrar erro para análise
        supabaseErrorHandler.handleError(error, 'database');
        return;
      }

      console.log(`🚗 Encontrados ${drivers?.length || 0} motoristas disponíveis`);
      callback(drivers || []);
    } catch (error) {
      console.error('Erro na busca de motoristas:', error);
      supabaseErrorHandler.handleError(error);
    }
  }

  // Atualizar localização do usuário de forma segura
  async updateUserLocation(userId: string, location: { lat: number; lng: number }) {
    if (!userId) {
      console.warn('⚠️ ID de usuário não fornecido para atualização de localização');
      return;
    }

    try {
      // Usar serviço de sincronização para garantir persistência
      syncService.upsert('user_locations', {
        user_id: userId,
        latitude: location.lat,
        longitude: location.lng,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });

      console.log('📍 Localização do usuário atualizada', {
        lat: location.lat.toFixed(6),
        lng: location.lng.toFixed(6)
      });
    } catch (error) {
      console.error('Erro ao atualizar localização:', error);
      supabaseErrorHandler.handleError(error, 'database');
    }
  }
}

// Iniciar realtime ao carregar
if (typeof window !== 'undefined') {
  realtimeManager.connect().then(success => {
    if (success) {
      console.log('✅ Conexão realtime inicializada com sucesso');
    }
  });
}

// Exportar tudo necessário para o app
export {
  supabase,
  TABLES,
  realtimeManager,
  supabaseErrorHandler,
  syncService
};

export default supabase;
