import { supabase, TABLES, supabaseHelpers } from '../../../shared/src/services/supabase-client';

// Configuração específica do app de passageiro
const APP_CONFIG = {
  appType: 'passenger',
  appVersion: '2.0.0',
  realtime: {
    autoconnect: false, // Conectar apenas quando necessário
  }
} as const;
// Serviço simplificado de localização
export class SimpleLocationService {
  private isTracking = false;
  private pollInterval: NodeJS.Timeout | null = null;

  // Iniciar tracking de motoristas próximos
  startDriverTracking(userLocation: { lat: number; lng: number }, callback: (drivers: any[]) => void) {
    try {
      console.log('🚗 Iniciando tracking de motoristas');

      // Parar tracking anterior se existir
      this.stopDriverTracking();

      this.isTracking = true;

      // Buscar motoristas iniciais
      this.fetchNearbyDrivers(userLocation, callback);

      // Configurar polling a cada 30 segundos
      this.pollInterval = setInterval(() => {
        if (this.isTracking) {
          this.fetchNearbyDrivers(userLocation, callback);
        }
      }, 30000);

      console.log('✅ Tracking de motoristas ativo');
    } catch (error) {
      console.error('Erro ao iniciar tracking:', error);
    }
  }

  // Parar tracking
  stopDriverTracking() {
    try {
      if (this.pollInterval) {
        clearInterval(this.pollInterval);
        this.pollInterval = null;
      }

      this.isTracking = false;
      console.log('🛑 Tracking de motoristas parado');
    } catch (error) {
      console.error('Erro ao parar tracking:', error);
      this.pollInterval = null;
      this.isTracking = false;
    }
  }

  // Buscar motoristas próximos
  private async fetchNearbyDrivers(_userLocation: { lat: number; lng: number }, callback: (drivers: any[]) => void) {
    try {
      const { data: drivers, error } = await supabase
        .from(TABLES.DRIVER_LOCATIONS)
        .select('*')
        .eq('is_available', true)
        .limit(10);

      if (error) {
        console.error('Erro ao buscar motoristas:', error);
        return;
      }

      console.log(`🚗 Encontrados ${drivers?.length || 0} motoristas disponíveis`);
      callback(drivers || []);
    } catch (error) {
      console.error('Erro na busca de motoristas:', error);
    }
  }

  // Atualizar localização do usuário
  async updateUserLocation(userId: string, location: { lat: number; lng: number }) {
    if (!userId) {
      console.warn('⚠️ ID de usuário não fornecido para atualização de localização');
      return;
    }

    try {
      const { error } = await supabase
        .from('user_locations')
        .upsert({
          user_id: userId,
          latitude: location.lat,
          longitude: location.lng,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        });

      if (error) {
        console.error('Erro ao atualizar localização:', error);
        return;
      }

      console.log('📍 Localização do usuário atualizada', {
        lat: location.lat.toFixed(6),
        lng: location.lng.toFixed(6)
      });
    } catch (error) {
      console.error('Erro ao atualizar localização:', error);
    }
  }
}

// Log de inicialização
if (process.env.NODE_ENV === 'development') {
  console.log(`📱 MobiDrive Passenger App v${APP_CONFIG.appVersion} - Supabase configurado`);
}

// Exportar tudo necessário para o app
export {
  supabase,
  TABLES,
  supabaseHelpers
};

export default supabase;
