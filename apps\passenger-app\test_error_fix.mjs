// =====================================================
// TESTE DA CORREÇÃO DO ERRO
// Verifica se o erro do container foi corrigido
// =====================================================

import puppeteer from 'puppeteer'

async function testErrorFix() {
  console.log('🔧 TESTANDO CORREÇÃO DO ERRO DO CONTAINER')
  console.log('=' .repeat(50))

  let browser = null
  
  try {
    browser = await puppeteer.launch({
      headless: false,
      devtools: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    })

    const page = await browser.newPage()
    await page.setViewport({ width: 1280, height: 720 })

    let errorCount = 0
    let successCount = 0

    // Monitorar console
    page.on('console', (msg) => {
      const text = msg.text()
      
      if (text.includes('CONTAINER .device-wrapper-container NOT FOUND')) {
        errorCount++
        console.log(`❌ [${errorCount}] ${text}`)
      } else if (text.includes('✅')) {
        successCount++
        console.log(`✅ [${successCount}] ${text}`)
      }
    })

    console.log('\n🌐 Carregando página de login...')
    await page.goto('http://localhost:3000/login', { 
      waitUntil: 'domcontentloaded',
      timeout: 15000 
    })
    
    console.log('⏱️ Aguardando 10 segundos para capturar logs...')
    await new Promise(resolve => setTimeout(resolve, 10000))
    
    console.log('\n📊 RESULTADO DO TESTE:')
    console.log(`❌ Erros do container: ${errorCount}`)
    console.log(`✅ Logs de sucesso: ${successCount}`)
    
    if (errorCount === 0) {
      console.log('\n🎉 CORREÇÃO FUNCIONOU!')
      console.log('✅ Erro do container foi eliminado')
    } else if (errorCount < 5) {
      console.log('\n⚠️ MELHORIA PARCIAL')
      console.log('✅ Erro reduzido significativamente')
    } else {
      console.log('\n❌ CORREÇÃO NÃO FUNCIONOU')
      console.log('⚠️ Erro ainda persiste')
    }
    
    return errorCount === 0

  } catch (error) {
    console.error('💥 Erro no teste:', error.message)
    return false
  } finally {
    if (browser) {
      await browser.close()
    }
  }
}

testErrorFix()
  .then((success) => {
    console.log('\n🏁 TESTE DA CORREÇÃO CONCLUÍDO!')
    process.exit(success ? 0 : 1)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
