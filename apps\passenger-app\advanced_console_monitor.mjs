// =====================================================
// MONITOR AVANÇADO DO CONSOLE - MÚLTIPLAS PÁGINAS
// Testa todas as páginas da aplicação com análise detalhada
// =====================================================

import puppeteer from 'puppeteer'

class AdvancedConsoleMonitor {
  constructor() {
    this.browser = null
    this.results = []
  }

  async init() {
    console.log('🚀 Iniciando Monitor Avançado do Console')
    console.log('=' .repeat(60))
    
    this.browser = await puppeteer.launch({
      headless: false,
      devtools: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    })
    
    console.log('✅ Navegador iniciado')
  }

  async testPage(url, pageName, duration = 15000) {
    console.log(`\n🧪 TESTANDO: ${pageName}`)
    console.log(`🔗 URL: ${url}`)
    console.log('=' .repeat(50))
    
    const page = await this.browser.newPage()
    await page.setViewport({ width: 1280, height: 720 })
    
    const pageData = {
      name: pageName,
      url: url,
      logs: [],
      errors: [],
      warnings: [],
      networkIssues: [],
      performance: {},
      startTime: Date.now()
    }

    // Configurar listeners
    page.on('console', (msg) => {
      const logEntry = {
        type: msg.type(),
        text: msg.text(),
        timestamp: new Date().toISOString(),
        timeFromStart: Date.now() - pageData.startTime
      }
      
      pageData.logs.push(logEntry)
      
      const timeStr = `[${Math.floor(logEntry.timeFromStart / 1000)}s]`
      
      switch (msg.type()) {
        case 'error':
          pageData.errors.push(logEntry)
          console.log(`${timeStr} ❌ ${msg.text()}`)
          break
        case 'warning':
          pageData.warnings.push(logEntry)
          console.log(`${timeStr} ⚠️ ${msg.text()}`)
          break
        case 'log':
          if (msg.text().includes('❌') || msg.text().includes('ERROR')) {
            pageData.errors.push(logEntry)
            console.log(`${timeStr} 🔴 ${msg.text()}`)
          } else if (msg.text().includes('⚠️') || msg.text().includes('WARNING')) {
            pageData.warnings.push(logEntry)
            console.log(`${timeStr} 🟡 ${msg.text()}`)
          } else if (msg.text().includes('✅') || msg.text().includes('SUCCESS')) {
            console.log(`${timeStr} 🟢 ${msg.text()}`)
          } else {
            console.log(`${timeStr} 📝 ${msg.text()}`)
          }
          break
        default:
          console.log(`${timeStr} 🔍 [${msg.type().toUpperCase()}] ${msg.text()}`)
      }
    })

    page.on('pageerror', (error) => {
      const errorEntry = {
        type: 'pageerror',
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        timeFromStart: Date.now() - pageData.startTime
      }
      
      pageData.errors.push(errorEntry)
      const timeStr = `[${Math.floor(errorEntry.timeFromStart / 1000)}s]`
      console.log(`${timeStr} 💥 PAGE ERROR: ${error.message}`)
    })

    page.on('requestfailed', (request) => {
      const failedRequest = {
        url: request.url(),
        method: request.method(),
        failure: request.failure()?.errorText,
        timestamp: new Date().toISOString(),
        timeFromStart: Date.now() - pageData.startTime
      }
      
      pageData.networkIssues.push(failedRequest)
      const timeStr = `[${Math.floor(failedRequest.timeFromStart / 1000)}s]`
      console.log(`${timeStr} 🌐 FAILED: ${request.method()} ${request.url()}`)
    })

    page.on('response', (response) => {
      if (response.status() >= 400) {
        const errorResponse = {
          url: response.url(),
          status: response.status(),
          statusText: response.statusText(),
          timestamp: new Date().toISOString(),
          timeFromStart: Date.now() - pageData.startTime
        }
        
        pageData.networkIssues.push(errorResponse)
        const timeStr = `[${Math.floor(errorResponse.timeFromStart / 1000)}s]`
        console.log(`${timeStr} 🔴 HTTP ${response.status()}: ${response.url()}`)
      }
    })

    try {
      // Navegar para a página
      console.log('🌐 Navegando...')
      const navigationStart = Date.now()
      
      await page.goto(url, { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      })
      
      const navigationTime = Date.now() - navigationStart
      pageData.performance.navigationTime = navigationTime
      
      console.log(`✅ Página carregada em ${navigationTime}ms`)
      
      // Aguardar e capturar métricas
      console.log(`⏱️ Monitorando por ${duration / 1000}s...`)
      await new Promise(resolve => setTimeout(resolve, duration))
      
      // Capturar métricas de performance
      const metrics = await page.metrics()
      pageData.performance.metrics = metrics
      
      // Capturar informações da página
      const pageInfo = await page.evaluate(() => {
        return {
          title: document.title,
          url: window.location.href,
          readyState: document.readyState,
          loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
          domElements: document.querySelectorAll('*').length,
          errors: window.errors || []
        }
      })
      
      pageData.pageInfo = pageInfo
      pageData.totalTime = Date.now() - pageData.startTime
      
      console.log(`📄 Título: ${pageInfo.title}`)
      console.log(`🔗 URL final: ${pageInfo.url}`)
      console.log(`📊 Elementos DOM: ${pageInfo.domElements}`)
      console.log(`⏱️ Tempo total: ${pageData.totalTime}ms`)
      
    } catch (error) {
      console.error(`💥 Erro durante teste: ${error.message}`)
      pageData.error = error.message
    } finally {
      await page.close()
    }

    // Análise da página
    this.analyzePage(pageData)
    this.results.push(pageData)
    
    return pageData
  }

  analyzePage(pageData) {
    console.log('\n📊 ANÁLISE DA PÁGINA:')
    console.log('-' .repeat(30))
    console.log(`📝 Total de logs: ${pageData.logs.length}`)
    console.log(`❌ Erros: ${pageData.errors.length}`)
    console.log(`⚠️ Warnings: ${pageData.warnings.length}`)
    console.log(`🌐 Problemas de rede: ${pageData.networkIssues.length}`)
    
    // Classificar saúde da página
    let health = 'EXCELENTE'
    if (pageData.errors.length > 0) {
      health = 'PROBLEMÁTICA'
    } else if (pageData.warnings.length > 3) {
      health = 'ATENÇÃO'
    } else if (pageData.warnings.length > 0) {
      health = 'BOA'
    }
    
    console.log(`🏥 Saúde da página: ${health}`)
    
    if (pageData.errors.length > 0) {
      console.log('\n❌ ERROS ENCONTRADOS:')
      pageData.errors.slice(0, 3).forEach((error, i) => {
        console.log(`  ${i + 1}. ${error.text || error.message}`)
      })
    }
  }

  async testAllPages() {
    const pages = [
      { url: 'http://localhost:3000', name: 'Home Page' },
      { url: 'http://localhost:3000/ride-request/map', name: 'Map Selection' },
      { url: 'http://localhost:3000/ride-request/details', name: 'Trip Details' },
      { url: 'http://localhost:3000/login', name: 'Login Page' }
    ]

    console.log(`🎯 TESTANDO ${pages.length} PÁGINAS DA APLICAÇÃO`)
    console.log('=' .repeat(60))

    for (let i = 0; i < pages.length; i++) {
      const pageConfig = pages[i]
      
      try {
        await this.testPage(pageConfig.url, pageConfig.name, 12000)
        
        if (i < pages.length - 1) {
          console.log('\n⏸️ Pausa entre testes...')
          await new Promise(resolve => setTimeout(resolve, 2000))
        }
        
      } catch (error) {
        console.error(`💥 Erro no teste da página ${pageConfig.name}:`, error.message)
      }
    }
  }

  generateFinalReport() {
    console.log('\n' + '=' .repeat(80))
    console.log('📊 RELATÓRIO FINAL COMPLETO')
    console.log('=' .repeat(80))

    let totalLogs = 0
    let totalErrors = 0
    let totalWarnings = 0
    let totalNetworkIssues = 0

    this.results.forEach((result, index) => {
      console.log(`\n📄 ${index + 1}. ${result.name}`)
      console.log(`   🔗 ${result.url}`)
      console.log(`   📝 Logs: ${result.logs.length}`)
      console.log(`   ❌ Erros: ${result.errors.length}`)
      console.log(`   ⚠️ Warnings: ${result.warnings.length}`)
      console.log(`   🌐 Rede: ${result.networkIssues.length}`)
      console.log(`   ⏱️ Tempo: ${result.totalTime}ms`)

      totalLogs += result.logs.length
      totalErrors += result.errors.length
      totalWarnings += result.warnings.length
      totalNetworkIssues += result.networkIssues.length
    })

    console.log('\n🎯 TOTAIS GERAIS:')
    console.log(`📝 Total de logs: ${totalLogs}`)
    console.log(`❌ Total de erros: ${totalErrors}`)
    console.log(`⚠️ Total de warnings: ${totalWarnings}`)
    console.log(`🌐 Total de problemas de rede: ${totalNetworkIssues}`)

    // Classificação geral
    let overallHealth = 'EXCELENTE'
    if (totalErrors > 0) {
      overallHealth = 'PROBLEMÁTICA'
    } else if (totalWarnings > 10) {
      overallHealth = 'ATENÇÃO'
    } else if (totalWarnings > 5) {
      overallHealth = 'BOA'
    }

    console.log(`\n🏥 SAÚDE GERAL DA APLICAÇÃO: ${overallHealth}`)

    if (totalErrors === 0) {
      console.log('\n🎉 PARABÉNS! Nenhum erro crítico encontrado!')
      console.log('✅ A aplicação está funcionando perfeitamente!')
    } else {
      console.log('\n⚠️ Foram encontrados erros que precisam ser corrigidos.')
    }

    return {
      totalLogs,
      totalErrors,
      totalWarnings,
      totalNetworkIssues,
      overallHealth,
      results: this.results
    }
  }

  async close() {
    if (this.browser) {
      await this.browser.close()
      console.log('\n🔒 Navegador fechado')
    }
  }
}

// Executar teste completo
async function runCompleteTest() {
  const monitor = new AdvancedConsoleMonitor()
  
  try {
    await monitor.init()
    await monitor.testAllPages()
    const report = monitor.generateFinalReport()
    
    return report
    
  } finally {
    await monitor.close()
  }
}

// Executar se chamado diretamente
runCompleteTest()
  .then((report) => {
    console.log('\n🏁 TESTE COMPLETO CONCLUÍDO!')
    process.exit(report.totalErrors > 0 ? 1 : 0)
  })
  .catch((error) => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
