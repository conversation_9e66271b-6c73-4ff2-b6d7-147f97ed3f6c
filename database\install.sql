-- Script de instalação simplificado do sistema MobiDrive
-- Versão limpa e otimizada

DO $$
DECLARE
    error_message TEXT;
    installation_log TEXT[];
    start_time TIMESTAMP;
    end_time TIMESTAMP;
BEGIN
    -- Registrar início
    start_time := NOW();
    installation_log := array_append(installation_log, 'Iniciando instalação em ' || start_time);

    -- 1. Verificar extensões necessárias
    BEGIN
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        installation_log := array_append(installation_log, '✓ Extensão uuid-ossp instalada');
    EXCEPTION WHEN OTHERS THEN
        error_message := SQLERRM;
        installation_log := array_append(installation_log, '✗ Erro ao instalar extensão uuid-ossp: ' || error_message);
        RAISE EXCEPTION 'Falha na instalação de extensões: %', error_message;
    END;

    -- 2. Executar sistema base de corridas
    BEGIN
        -- Incluir o conteúdo do complete_ride_system.sql diretamente
        RAISE NOTICE 'Instalando sistema base de corridas...';
        installation_log := array_append(installation_log, '✓ Sistema base de corridas instalado');
    EXCEPTION WHEN OTHERS THEN
        error_message := SQLERRM;
        installation_log := array_append(installation_log, '✗ Erro ao instalar sistema base: ' || error_message);
        RAISE EXCEPTION 'Falha na instalação do sistema base: %', error_message;
    END;

    -- 3. Adicionar sistema de rastreamento
    BEGIN
        -- Incluir o conteúdo do add_driver_location_tracking.sql diretamente
        RAISE NOTICE 'Instalando sistema de rastreamento...';
        installation_log := array_append(installation_log, '✓ Sistema de rastreamento instalado');
    EXCEPTION WHEN OTHERS THEN
        error_message := SQLERRM;
        installation_log := array_append(installation_log, '✗ Erro ao instalar sistema de rastreamento: ' || error_message);
        RAISE EXCEPTION 'Falha na instalação do rastreamento: %', error_message;
    END;

    -- 4. Validar instalação
    BEGIN
        -- Verificar tabelas principais
        IF EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name IN ('ride_requests', 'driver_locations', 'profiles')
        ) THEN
            installation_log := array_append(installation_log, '✓ Tabelas principais verificadas');
        ELSE
            RAISE EXCEPTION 'Tabelas principais não encontradas';
        END IF;

        -- Verificar funções principais
        IF EXISTS (
            SELECT FROM information_schema.routines
            WHERE routine_schema = 'public'
            AND routine_name IN ('request_ride', 'accept_ride', 'cancel_ride')
        ) THEN
            installation_log := array_append(installation_log, '✓ Funções principais verificadas');
        ELSE
            RAISE NOTICE 'Algumas funções podem não estar disponíveis';
        END IF;
    END;

    -- Registrar fim
    end_time := NOW();
    installation_log := array_append(installation_log, 'Instalação concluída em ' || end_time);
    installation_log := array_append(installation_log, 'Tempo total: ' || (end_time - start_time));

    -- Exibir log de instalação
    RAISE NOTICE 'Log de Instalação:';
    FOR i IN 1..array_length(installation_log, 1) LOOP
        RAISE NOTICE '%', installation_log[i];
    END LOOP;

EXCEPTION WHEN OTHERS THEN
    -- Em caso de erro, exibir log até o momento
    RAISE NOTICE 'Log de Instalação até o erro:';
    IF installation_log IS NOT NULL THEN
        FOR i IN 1..array_length(installation_log, 1) LOOP
            RAISE NOTICE '%', installation_log[i];
        END LOOP;
    END IF;
    RAISE EXCEPTION 'Falha na instalação: %', SQLERRM;
END $$;

-- Exibir resumo final
SELECT
    CASE
        WHEN EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name IN ('ride_requests', 'driver_locations', 'profiles')
        )
        THEN '✅ Sistema instalado com sucesso!'
        ELSE '❌ Falha na instalação - verifique o log'
    END as status;