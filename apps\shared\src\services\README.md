# Supabase Communication Infrastructure

Esta infraestrutura fornece uma comunicação robusta e confiável entre seus aplicativos e o Supabase, garantindo sincronização contínua mesmo em condições de rede instáveis.

## Principais Componentes

### 1. Cliente Supabase Otimizado (`supabase-client.ts`)

Cliente principal com configurações otimizadas para performance e confiabilidade:
- Reconexão automática
- Tratamento robusto de erros
- Detecção de status de rede

```typescript
import { supabase, supabaseHelpers } from '../services/supabase-client';

// Uso básico
const { data, error } = await supabase.from('profiles').select('*');

// Com retry automático
const result = await supabaseHelpers.withRetry(async () => {
  return supabase.from('profiles').select('*');
});
```

### 2. Gerenciador Realtime (`supabase-realtime-manager.ts`)

Gerencia conexões realtime de forma confiável:
- Reconexão automática
- Tratamento de erros
- Limpeza adequada de recursos

```typescript
import { realtimeManager } from '../services/supabase-realtime-manager';

// Inscrever-se em um canal com robustez
const channel = realtimeManager.subscribe(
  'table-updates',
  'postgres_changes',
  'UPDATE',
  (payload) => {
    console.log('Dados atualizados:', payload);
  },
  { criticalChannel: true }
);
```

### 3. Serviço de Sincronização (`supabase-sync-service.ts`)

Garante que operações sejam enviadas ao servidor mesmo em conexões instáveis:
- Enfileiramento de operações
- Retry automático
- Persistência local

```typescript
import { syncService } from '../services/supabase-sync-service';

// Inserir dados com garantia de entrega
syncService.insert('profiles', {
  user_id: '123',
  name: 'João Silva',
  email: '<EMAIL>'
});

// Forçar sincronização
syncService.sync();
```

### 4. Monitor de Saúde (`supabase-health-monitor.ts`)

Monitora a saúde da conexão com o Supabase:
- Detecção de problemas
- Reconexão automática
- Notificações

```typescript
import { healthMonitor } from '../services/supabase-health-monitor';

// Verificar saúde da conexão
const healthCheck = await healthMonitor.checkHealth();
console.log('Status da conexão:', healthCheck.status);

// Adicionar listener para mudanças de status
const listenerId = healthMonitor.addListener((check) => {
  console.log('Status atualizado:', check.status);
});
```

### 5. Gerenciador de Erros (`supabase-error-handler.ts`)

Centraliza o tratamento de erros:
- Classificação de erros
- Tentativa de correção automática
- Registro para análise

```typescript
import { supabaseErrorHandler, SupabaseErrorType } from '../services/supabase-error-handler';

try {
  // Operação que pode falhar
  const result = await supabase.from('profiles').select('*');
  if (result.error) throw result.error;
} catch (error) {
  // Tratar erro de forma centralizada
  supabaseErrorHandler.handleError(error, SupabaseErrorType.DATABASE);
}
```

### 6. Gerenciador de Requisições (`supabase-request-manager.ts`)

Controla a concorrência de requisições:
- Limita requisições simultâneas
- Priorização de requisições
- Evita sobrecarga do servidor

```typescript
import { withRequestManager } from '../services/supabase-request-manager';

// Executar operação com controle de concorrência
const result = await withRequestManager(
  () => supabase.from('profiles').select('*'),
  { priority: 1 } // Alta prioridade
);
```

### 7. Serviço de Funções Edge (`supabase-functions-service.ts`)

Facilita o uso de funções Edge do Supabase:
- Retry automático
- Timeout configurável
- Cache opcional

```typescript
import { functionsService } from '../services/supabase-functions-service';

// Chamar função Edge com retry automático
const result = await functionsService.callFunction('calculate-price', {
  body: { distance: 10, time: 15 },
  retryCount: 3,
  timeout: 10000
});

// Com cache
const cachedResult = await functionsService.callFunctionWithCache('get-config', {
  ttl: 60000 // Cache por 1 minuto
});
```

## Hooks React

Hooks para facilitar o uso em componentes React:

### `useSupabaseQuery`

```typescript
import { useSupabaseSelect } from '../hooks/useSupabaseQuery';

function UserList() {
  const { data, isLoading, error, refetch } = useSupabaseSelect('profiles', {
    select: 'id, name, email',
    orderBy: 'created_at',
    limit: 10
  });
  
  if (isLoading) return <div>Carregando...</div>;
  if (error) return <div>Erro: {error.message}</div>;
  
  return (
    <div>
      <button onClick={refetch}>Atualizar</button>
      {data.map(user => (
        <div key={user.id}>{user.name}</div>
      ))}
    </div>
  );
}
```

### `useSupabaseRealtime`

```typescript
import { useSupabaseTable } from '../hooks/useSupabaseRealtime';

function RealTimeUsers() {
  const { data } = useSupabaseTable('profiles', 'UPDATE', {
    criticalChannel: true
  });
  
  useEffect(() => {
    if (data) {
      console.log('Usuário atualizado:', data);
    }
  }, [data]);
  
  return <div>Monitorando atualizações...</div>;
}
```

## Componentes UI

### `SupabaseConnectionStatus`

Exibe o status da conexão com o Supabase:

```tsx
import { SupabaseConnectionStatus } from '../components/SupabaseConnectionStatus';

function App() {
  return (
    <div>
      <h1>Meu App</h1>
      <SupabaseConnectionStatus 
        position="bottom-right" 
        compact={true} 
        showDetails={true} 
      />
    </div>
  );
}
```

### `SyncStatusIndicator`

Exibe o status de sincronização:

```tsx
import { SyncStatusIndicator } from '../components/SyncStatusIndicator';

function App() {
  return (
    <div>
      <h1>Meu App</h1>
      <SyncStatusIndicator 
        position="bottom-left" 
        compact={true}
        showDetails={true}
        onSyncComplete={() => console.log('Sincronização concluída')}
      />
    </div>
  );
}
```

## Inicialização

Para inicializar todos os serviços:

```typescript
import { initializeSupabaseServices } from '../services';

// Inicializar todos os serviços com configurações padrão
initializeSupabaseServices();

// Ou com configurações personalizadas
initializeSupabaseServices({
  enableRealtime: true,
  enableHealthMonitor: true,
  enableSyncService: true,
  maxConcurrentRequests: 8
});
```

## Melhores Práticas

1. **Sempre use os hooks e componentes fornecidos** para obter o máximo de confiabilidade
2. **Considere a prioridade das operações** - use prioridade alta para operações críticas
3. **Implemente indicadores de status** para manter os usuários informados
4. **Aproveite o serviço de sincronização** para operações que não podem falhar
5. **Monitore a saúde da conexão** e adapte a interface de acordo