#!/usr/bin/env node

/**
 * Script de teste de conexão com Supabase
 * Usa variáveis de ambiente para segurança
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Carrega variáveis de ambiente
dotenv.config()

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.VITE_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY

// Verifica se as variáveis estão configuradas
if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Erro: Variáveis de ambiente não configuradas!')
  console.error('Configure as seguintes variáveis:')
  console.error('- NEXT_PUBLIC_SUPABASE_URL ou VITE_SUPABASE_URL')
  console.error('- NEXT_PUBLIC_SUPABASE_ANON_KEY ou VITE_SUPABASE_ANON_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testConnection() {
  console.log('🔄 Testando conexão com Supabase...')
  console.log('URL:', supabaseUrl)
  console.log('Chave:', supabaseAnonKey.substring(0, 20) + '...')
  console.log('')
  
  const results = {
    connection: false,
    auth: false,
    tables: {},
    errors: []
  }
  
  try {
    // Teste 1: Verificar conexão básica
    console.log('📡 Teste 1: Conexão básica...')
    const { data, error } = await supabase.from('ride_requests').select('count').limit(1)
    
    if (error) {
      console.log('❌ Erro na conexão:', error.message)
      results.errors.push({ test: 'connection', error: error.message })
    } else {
      console.log('✅ Conexão estabelecida com sucesso!')
      results.connection = true
    }
    
    // Teste 2: Verificar autenticação
    console.log('\n🔐 Teste 2: Sistema de autenticação...')
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError && authError.message !== 'Auth session missing!') {
      console.log('❌ Erro na autenticação:', authError.message)
      results.errors.push({ test: 'auth', error: authError.message })
    } else {
      console.log('✅ Sistema de autenticação funcionando')
      results.auth = true
      if (user) {
        console.log('👤 Usuário autenticado:', user.email)
      } else {
        console.log('ℹ️ Nenhum usuário autenticado (normal para teste)')
      }
    }
    
    // Teste 3: Verificar tabelas principais
    console.log('\n🗄️ Teste 3: Verificando tabelas...')
    
    const tables = [
      'ride_requests',
      'driver_locations', 
      'driver_profiles',
      'profiles'
    ]
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase.from(table).select('*').limit(1)
        if (error) {
          console.log(`❌ Tabela '${table}': ${error.message}`)
          results.tables[table] = { accessible: false, error: error.message }
        } else {
          console.log(`✅ Tabela '${table}': Acessível`)
          results.tables[table] = { accessible: true, records: data?.length || 0 }
        }
      } catch (err) {
        console.log(`❌ Tabela '${table}': Erro inesperado - ${err.message}`)
        results.tables[table] = { accessible: false, error: err.message }
      }
    }
    
    // Teste 4: Verificar realtime
    console.log('\n⚡ Teste 4: Verificando realtime...')
    try {
      const channel = supabase.channel('test-channel')
      console.log('✅ Realtime disponível')
      channel.unsubscribe()
    } catch (err) {
      console.log('❌ Erro no realtime:', err.message)
      results.errors.push({ test: 'realtime', error: err.message })
    }
    
  } catch (error) {
    console.error('❌ Erro geral:', error)
    results.errors.push({ test: 'general', error: error.message })
  }
  
  // Resumo final
  console.log('\n📊 RESUMO DOS TESTES:')
  console.log('='.repeat(50))
  console.log(`Conexão: ${results.connection ? '✅' : '❌'}`)
  console.log(`Autenticação: ${results.auth ? '✅' : '❌'}`)
  
  const accessibleTables = Object.values(results.tables).filter(t => t.accessible).length
  const totalTables = Object.keys(results.tables).length
  console.log(`Tabelas: ${accessibleTables}/${totalTables} acessíveis`)
  
  if (results.errors.length > 0) {
    console.log(`\n❌ Erros encontrados: ${results.errors.length}`)
    results.errors.forEach((err, i) => {
      console.log(`${i + 1}. ${err.test}: ${err.error}`)
    })
  }
  
  const overallSuccess = results.connection && results.auth && accessibleTables > 0
  console.log(`\n${overallSuccess ? '🎉 SUCESSO' : '⚠️ PROBLEMAS ENCONTRADOS'}: Sistema ${overallSuccess ? 'funcionando' : 'com problemas'}`)
  
  return results
}

// Executar teste se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  testConnection()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('💥 Falha crítica:', error)
      process.exit(1)
    })
}

export { testConnection }
