import React, { useRef, useEffect, useCallback, useState } from 'react'
import mapboxgl from 'mapbox-gl'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  MapPin, 
  Navigation, 
  AlertCircle, 
  RefreshCw, 
  Clock, 
  DollarSign, 
  X,
  Loader2,
  Car as CarIcon,
  Bike
} from 'lucide-react'
import { useMapboxSearch } from '../hooks/useMapboxSearch'
import { useRideEstimate } from '../hooks/useRideEstimate'
import { MapboxError } from '../services/MapboxService'

// Configure Mapbox token from environment
const MAPBOX_TOKEN = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN
if (!MAPBOX_TOKEN) {
  console.error('❌ VITE_MAPBOX_ACCESS_TOKEN não configurado!')
}
mapboxgl.accessToken = MAPBOX_TOKEN

interface EnhancedMapboxComponentProps {
  className?: string
  onRideRequest?: (rideData: any) => void
  destinationOnly?: boolean
  showControls?: boolean
  nearbyDrivers?: DriverLocation[]
}

interface DriverLocation {
  id: string
  name: string
  rating: number
  eta: number
  coordinates: [number, number]
  vehicle: {
    model: string
    color: string
    plate: string
  }
}

const EnhancedMapboxComponent: React.FC<EnhancedMapboxComponentProps> = ({
  className = '',
  onRideRequest,
  destinationOnly = true,
  showControls = true,
  nearbyDrivers = []
}) => {
  // Map refs and state
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<mapboxgl.Map | null>(null)
  const markers = useRef<{
    user?: mapboxgl.Marker
    destination?: mapboxgl.Marker
    drivers: mapboxgl.Marker[]
  }>({ drivers: [] })

  // Component state
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null)
  const [isMapLoaded, setIsMapLoaded] = useState(false)
  const [isInitializing, setIsInitializing] = useState(true)
  const [mapError, setMapError] = useState<string | null>(null)
  const [showSearchResults, setShowSearchResults] = useState(false)
  const [isDragMode, setIsDragMode] = useState(false)
  const [dragMarker, setDragMarker] = useState<mapboxgl.Marker | null>(null)

  // Route constants
  const routeSourceId = 'route-source'
  const routeLayerId = 'route-layer'

  // Hooks
  const {
    searchQuery,
    searchResults,
    searchError,
    origin,
    destination,
    searchPlaces,
    selectResult,
    setOrigin,
    setDestination,
    getCurrentLocation
  } = useMapboxSearch({ userLocation: userLocation || undefined })

  const {
    rideEstimate,
    isCalculatingRide,
    calculateRideEstimate
  } = useRideEstimate()

  // Map click handler for destination selection
  const handleMapClick = useCallback(async (e: mapboxgl.MapMouseEvent) => {
    console.log('🗺️ Clique no mapa detectado! isDragMode:', isDragMode, 'map.current:', !!map.current)
    
    if (!isDragMode || !map.current) {
      console.log('❌ Clique ignorado - modo não ativo ou mapa não carregado')
      return
    }

    console.log('🎯 Modo seleção ativo - processando clique:', e.lngLat)

    const coordinates: [number, number] = [e.lngLat.lng, e.lngLat.lat]

    // Remove existing drag marker
    if (dragMarker) {
      dragMarker.remove()
      setDragMarker(null)
    }

    // Show immediate visual feedback
    const tempMarker = new mapboxgl.Marker({
      color: '#ef4444',
      scale: 1.2
    })
      .setLngLat(coordinates)
      .addTo(map.current)

    try {
      // Reverse geocode to get address
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${coordinates[0]},${coordinates[1]}.json?access_token=${mapboxgl.accessToken}&language=pt&limit=1`
      )
      const data = await response.json()

      if (data.features && data.features.length > 0) {
        const feature = data.features[0]
        const result = {
          id: `map-selected-${Date.now()}`,
          place_name: feature.place_name,
          center: coordinates,
          place_type: feature.place_type || ['address'],
          properties: feature.properties || {}
        }

        // Set as destination
        setDestination(result)
        console.log('✅ Destino definido via mapa:', result.place_name)

        // Update search field with selected address
        selectResult(result)

        // Remove temporary marker
        tempMarker.remove()

        // Exit selection mode
        setIsDragMode(false)

        console.log('🎉 Localização selecionada com sucesso!')
      } else {
        throw new Error('Não foi possível obter endereço')
      }
    } catch (error) {
      console.error('❌ Erro ao processar seleção:', error)
      // Remove temporary marker on error
      tempMarker.remove()
      // Keep mode active for retry
    }
  }, [isDragMode, dragMarker, setDestination, selectResult])

  // Initialize map
  const initializeMap = useCallback(async (coords: [number, number]) => {
    if (!mapContainer.current || map.current) return

    try {
      setMapError(null)

      console.log('🔧 Inicializando mapa...')
      console.log('🔍 Token atual no mapboxgl:', mapboxgl.accessToken?.substring(0, 20) + '...')

      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: 'mapbox://styles/mapbox/dark-v11',
        center: coords,
        zoom: 14,
        attributionControl: false,
        antialias: true
      })

      map.current.on('load', () => {
        setIsMapLoaded(true)
        setIsInitializing(false)

        // Add user location marker
        if (markers.current.user) {
          markers.current.user.remove()
        }

        markers.current.user = new mapboxgl.Marker({
          color: '#3b82f6',
          scale: 1.2
        })
          .setLngLat(coords)
          .addTo(map.current!)

        // Set origin to current location
        setOrigin(coords)

        // Initialize route source
        if (!map.current!.getSource(routeSourceId)) {
          map.current!.addSource(routeSourceId, {
            type: 'geojson',
            data: {
              type: 'Feature',
              properties: {},
              geometry: {
                type: 'LineString',
                coordinates: []
              }
            }
          })

          map.current!.addLayer({
            id: routeLayerId,
            type: 'line',
            source: routeSourceId,
            layout: {
              'line-join': 'round',
              'line-cap': 'round'
            },
            paint: {
              'line-color': '#3b82f6',
              'line-width': 4,
              'line-opacity': 0.8
            }
          })
        }
      })

      map.current.on('error', (e) => {
        console.error('Map error:', e)
        setMapError('Erro ao carregar o mapa. Verifique sua conexão.')
      })

      // Add click handler for map selection
      map.current.on('click', handleMapClick)
      console.log('🎯 Event listener de clique registrado no mapa')

    } catch (error) {
      console.error('Error initializing map:', error)
      setMapError('Erro ao inicializar o mapa')
      setIsInitializing(false)
    }
  }, [setOrigin, handleMapClick])

  // Get user location
  useEffect(() => {
    const initLocation = async () => {
      try {
        setIsInitializing(true)

        console.log('🔧 Inicializando com token já configurado...')
        console.log('🔍 Token atual:', mapboxgl.accessToken?.substring(0, 20) + '...')

        const coords = await getCurrentLocation()
        setUserLocation(coords)
        await initializeMap(coords)

        // Always set origin as current location
        if (destinationOnly || true) {
          try {
            const response = await fetch(
              `https://api.mapbox.com/geocoding/v5/mapbox.places/${coords[0]},${coords[1]}.json?access_token=${mapboxgl.accessToken}&language=pt&limit=1`
            )
            const data = await response.json()

            if (data.features && data.features.length > 0) {
              const feature = data.features[0]
              const currentLocationData = {
                id: 'current-location',
                place_name: feature.place_name || 'Localização atual',
                center: coords,
                place_type: ['current_location'],
                properties: { ...feature.properties, is_current_location: true }
              }
              setOrigin(currentLocationData)
              console.log('📍 Origem definida como localização atual:', currentLocationData.place_name)
            } else {
              setOrigin({
                id: 'current-location-fallback',
                place_name: 'Localização atual',
                center: coords,
                place_type: ['current_location'],
                properties: { is_current_location: true } as any
              })
            }
          } catch (geocodeError) {
            console.warn('Erro ao geocodificar localização atual:', geocodeError)
            setOrigin({
              id: 'current-location-fallback',
              place_name: 'Localização atual',
              center: coords,
              place_type: ['current_location'],
              properties: { is_current_location: true } as any
            })
          }
        }
      } catch (error) {
        console.warn('Could not get user location:', error)

        // Fallback to São Paulo
        const fallbackCoords: [number, number] = [-46.6333, -23.5505]
        setUserLocation(fallbackCoords)
        await initializeMap(fallbackCoords)

        if (destinationOnly || true) {
          setOrigin({
            id: 'fallback-location',
            place_name: 'São Paulo, SP',
            center: fallbackCoords,
            place_type: ['fallback_location'],
            properties: { is_fallback_location: true } as any
          })
        }
      }
    }

    initLocation()

    return () => {
      if (map.current) {
        map.current.remove()
        map.current = null
      }
    }
  }, [getCurrentLocation, initializeMap, destinationOnly, setOrigin])

  // Update map markers and route when locations change
  useEffect(() => {
    if (!map.current || !isMapLoaded) return

    // Clear existing destination marker
    if (markers.current.destination) {
      markers.current.destination.remove()
      markers.current.destination = undefined
    }

    // Clear existing driver markers
    markers.current.drivers.forEach(marker => marker.remove())
    markers.current.drivers = []

    // Add enhanced destination marker
    if (destination) {
      const destEl = document.createElement('div')
      destEl.className = 'destination-marker'
      destEl.style.cssText = `
        width: 36px;
        height: 36px;
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
        border: 4px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4), 0 0 0 1px rgba(239, 68, 68, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: visible;
      `

      destEl.innerHTML = `
        <div style="
          color: white;
          font-size: 16px;
          font-weight: bold;
          text-shadow: 0 1px 2px rgba(0,0,0,0.3);
          position: relative;
          z-index: 2;
        ">🎯</div>
        <div style="
          position: absolute;
          top: -6px;
          left: -6px;
          width: 48px;
          height: 48px;
          background: radial-gradient(circle, rgba(239, 68, 68, 0.2) 0%, rgba(239, 68, 68, 0.1) 70%, transparent 100%);
          border-radius: 50%;
          animation: pulse 2s infinite;
          z-index: 1;
        "></div>
      `

      destEl.addEventListener('mouseenter', () => {
        destEl.style.transform = 'scale(1.15) translateY(-2px)'
        destEl.style.boxShadow = '0 8px 20px rgba(239, 68, 68, 0.6), 0 0 0 2px rgba(239, 68, 68, 0.2)'
      })

      destEl.addEventListener('mouseleave', () => {
        destEl.style.transform = 'scale(1) translateY(0px)'
        destEl.style.boxShadow = '0 6px 16px rgba(239, 68, 68, 0.4), 0 0 0 1px rgba(239, 68, 68, 0.1)'
      })

      markers.current.destination = new mapboxgl.Marker(destEl)
        .setLngLat(destination.center)
        .setPopup(new mapboxgl.Popup({
          offset: 35,
          className: 'destination-popup',
          closeButton: false
        }).setHTML(`
          <div class="p-3 bg-white rounded-xl shadow-lg border border-gray-100">
            <div class="flex items-center space-x-2">
              <span class="text-red-500 text-lg">🎯</span>
              <div>
                <h4 class="font-semibold text-gray-900 text-sm">Destino</h4>
                <p class="text-xs text-gray-600">${destination.place_name}</p>
              </div>
            </div>
          </div>
        `))
        .addTo(map.current!)
    }

    // Update route visualization
    if (origin && destination && rideEstimate?.route?.geometry) {
      const routeGeoJSON = {
        type: 'Feature' as const,
        properties: {},
        geometry: rideEstimate.route.geometry
      }

      const source = map.current!.getSource(routeSourceId) as mapboxgl.GeoJSONSource
      if (source) {
        source.setData(routeGeoJSON)
      }

      // Fit bounds to show entire route
      const bounds = new mapboxgl.LngLatBounds()
      bounds.extend(origin.center)
      bounds.extend(destination.center)

      map.current!.fitBounds(bounds, {
        padding: { top: 50, bottom: 50, left: 50, right: 50 },
        maxZoom: 15
      })
    } else {
      // Clear route if no destination
      const source = map.current!.getSource(routeSourceId) as mapboxgl.GeoJSONSource
      if (source) {
        source.setData({
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'LineString',
            coordinates: []
          }
        })
      }
    }
  }, [origin, destination, nearbyDrivers, rideEstimate, isMapLoaded])

  // Calculate ride estimate when origin and destination are set
  useEffect(() => {
    if (origin && destination) {
      calculateRideEstimate(origin.center, destination.center)
    }
  }, [origin, destination, calculateRideEstimate])

  const handleSearchInput = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setShowSearchResults(query.length > 0)

    try {
      await searchPlaces(query)
    } catch (error) {
      if (error instanceof MapboxError) {
        console.error('Search error:', error.message)
        if (error.code === 'RATE_LIMIT') {
          setMapError('Muitas buscas. Aguarde alguns segundos antes de tentar novamente.')
          setTimeout(() => setMapError(null), 5000)
        }
      }
    }
  }, [searchPlaces])

  const handleResultSelect = useCallback((result: any) => {
    selectResult(result)
    setDestination(result)
    setShowSearchResults(false)
  }, [selectResult, setDestination])

  // Generate vehicle options based on base price
  const getVehicleOptions = useCallback((basePrice: number) => {
    return [
      {
        id: 'economy',
        type: 'car',
        name: 'MobiEconomy',
        description: 'Opção mais econômica',
        price: basePrice,
        eta: 5,
        icon: CarIcon,
        color: 'from-blue-500 to-blue-600',
        features: ['4 lugares', 'Ar condicionado', 'Música']
      },
      {
        id: 'comfort',
        type: 'car',
        name: 'MobiComfort',
        description: 'Conforto premium',
        price: basePrice * 1.5,
        eta: 3,
        icon: CarIcon,
        color: 'from-purple-500 to-purple-600',
        features: ['4 lugares', 'Carro premium', 'Wi-Fi', 'Água']
      },
      {
        id: 'moto',
        type: 'motorcycle',
        name: 'MobiMoto',
        description: 'Rápido e prático',
        price: basePrice * 0.7,
        eta: 2,
        icon: Bike,
        color: 'from-green-500 to-green-600',
        features: ['1 pessoa', 'Capacete incluso', 'Super rápido']
      }
    ]
  }, [])

  // Handle vehicle selection
  const handleVehicleSelect = useCallback((selectedVehicle: any) => {
    if (origin && destination && rideEstimate && onRideRequest) {
      console.log('🚗 Veículo selecionado:', selectedVehicle)

      const serializableRideData = {
        origin: {
          id: origin.id,
          place_name: origin.place_name,
          center: origin.center,
          place_type: origin.place_type,
          properties: origin.properties
        },
        destination: {
          id: destination.id,
          place_name: destination.place_name,
          center: destination.center,
          place_type: destination.place_type,
          properties: destination.properties
        },
        estimate: {
          distance: rideEstimate.distance,
          duration: rideEstimate.duration,
          price: selectedVehicle.price,
          vehicleType: selectedVehicle.type,
          vehicleName: selectedVehicle.name
        },
        route: rideEstimate.route ? {
          distance: rideEstimate.route.distance,
          duration: rideEstimate.route.duration,
          geometry: rideEstimate.route.geometry
        } : null,
        selectedVehicle: {
          id: selectedVehicle.id,
          name: selectedVehicle.name,
          type: selectedVehicle.type,
          price: selectedVehicle.price,
          eta: selectedVehicle.eta,
          description: selectedVehicle.description,
          color: selectedVehicle.color
        }
      }

      onRideRequest(serializableRideData)
    }
  }, [origin, destination, rideEstimate, onRideRequest])

  // Toggle drag mode
  const toggleDragMode = useCallback(() => {
    setIsDragMode(prev => {
      const newMode = !prev
      console.log('🎯 Modo de seleção alterado:', prev, '->', newMode)

      // If exiting drag mode, clean up drag marker
      if (!newMode && dragMarker) {
        dragMarker.remove()
        setDragMarker(null)
      }
      return newMode
    })
    setShowSearchResults(false)
  }, [dragMarker])

  const retryMapLoad = useCallback(() => {
    if (userLocation) {
      setMapError(null)
      setIsInitializing(true)
      initializeMap(userLocation)
    }
  }, [userLocation, initializeMap])

  if (mapError) {
    return (
      <div className={`relative ${className}`}>
        <div className="w-full h-96 rounded-2xl bg-red-50 border border-red-200 flex items-center justify-center">
          <div className="text-center p-6">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-800 mb-2">Erro no Mapa</h3>
            <p className="text-red-600 text-sm mb-4">{mapError}</p>
            <button
              onClick={retryMapLoad}
              className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors flex items-center space-x-2 mx-auto"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Tentar Novamente</span>
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`}>
      {/* Map Container */}
      <div
        ref={mapContainer}
        className={`w-full h-[85vh] min-h-[700px] rounded-xl overflow-hidden shadow-2xl ${isDragMode ? 'cursor-crosshair' : 'cursor-default'}`}
        style={{
          background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
          minHeight: '700px',
          maxHeight: '90vh',
          cursor: isDragMode ? 'crosshair' : 'default',
          margin: '-1rem',
          width: 'calc(100% + 2rem)',
          borderRadius: '0.75rem'
        }}
      />

      {/* Loading Overlay */}
      {isInitializing && (
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/95 to-purple-50/95 backdrop-blur-md rounded-3xl flex items-center justify-center z-10">
          <motion.div
            className="text-center p-8 bg-white/80 rounded-2xl shadow-xl border border-white/50"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <Navigation className="w-12 h-12 text-blue-500 mx-auto mb-4" />
            </motion.div>
            <h3 className="text-xl font-bold text-gray-800 mb-2">Carregando MobiDrive</h3>
            <p className="text-gray-600 font-medium mb-1">Inicializando mapa inteligente...</p>
            <p className="text-gray-500 text-sm">Obtendo sua localização precisa</p>
            <div className="mt-4 w-48 h-1 bg-gray-200 rounded-full overflow-hidden mx-auto">
              <motion.div
                className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
                initial={{ width: "0%" }}
                animate={{ width: "100%" }}
                transition={{ duration: 3, repeat: Infinity }}
              />
            </div>
          </motion.div>
        </div>
      )}

      {/* Search Overlay */}
      <div className="absolute top-4 left-4 right-4 z-20">
        <div className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={handleSearchInput}
            placeholder="🎯 Para onde vamos?"
            className="w-full pl-4 pr-12 py-4 bg-white/95 backdrop-blur-sm border border-white/30 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 transition-all shadow-lg text-base font-medium"
          />

          {/* Map Pin Icon */}
          <motion.button
            onClick={toggleDragMode}
            className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-2 rounded-full transition-all ${
              isDragMode
                ? 'bg-blue-500 text-white shadow-lg'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-blue-600'
            }`}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            title={isDragMode ? 'Cancelar seleção no mapa' : 'Marcar no mapa'}
          >
            <MapPin className="w-4 h-4" />
          </motion.button>
        </div>

        {/* Drag Mode Feedback */}
        {isDragMode && (
          <motion.div
            className="mt-2 p-3 bg-blue-50/80 backdrop-blur-sm border border-blue-200 rounded-lg shadow-sm"
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <motion.div
                  className="w-2 h-2 bg-blue-500 rounded-full"
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 1, repeat: Infinity }}
                />
                <span className="text-blue-700 text-sm font-medium">
                  Clique no mapa para selecionar destino
                </span>
              </div>
              <motion.button
                onClick={() => setIsDragMode(false)}
                className="p-1 hover:bg-blue-100 rounded-full transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                title="Cancelar"
              >
                <X className="w-3 h-3 text-blue-600" />
              </motion.button>
            </div>
          </motion.div>
        )}

        {/* Search Error */}
        {searchError && (
          <motion.div
            className="mt-2 p-3 bg-red-50 border border-red-200 rounded-xl"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-4 h-4 text-red-500" />
              <span className="text-red-700 text-sm">{searchError}</span>
            </div>
          </motion.div>
        )}

        {/* Search Results */}
        <AnimatePresence>
          {showSearchResults && searchResults.length > 0 && (
            <motion.div
              className="mt-3 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-white/30 overflow-hidden max-h-80 overflow-y-auto"
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
            >
              {searchResults.map((result) => (
                <motion.button
                  key={result.id}
                  className="w-full px-5 py-4 text-left hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all border-b border-gray-100 last:border-b-0 group"
                  onClick={() => handleResultSelect(result)}
                  whileHover={{ x: 4 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-start space-x-4">
                    <div className="p-2 bg-gradient-to-br from-blue-100 to-purple-100 rounded-xl group-hover:from-blue-200 group-hover:to-purple-200 transition-all">
                      <MapPin className="w-5 h-5 text-blue-600 group-hover:text-blue-700" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-gray-900 font-semibold truncate text-base">
                        {result.place_name.split(',')[0]}
                      </p>
                      <p className="text-gray-500 text-sm truncate mt-1">
                        📍 {result.place_name.split(',').slice(1).join(',')}
                      </p>
                    </div>
                    <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="p-1 bg-blue-100 rounded-full">
                        <Navigation className="w-3 h-3 text-blue-600" />
                      </div>
                    </div>
                  </div>
                </motion.button>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Vehicle Options Panel */}
      {showControls && destination && rideEstimate && (
        <motion.div
          className="absolute bottom-4 left-4 right-4 z-20"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/30 overflow-hidden">
            {/* Header */}
            <div className="p-5 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-bold text-gray-900">Escolha seu veículo</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    📍 {rideEstimate.distance} • ⏱️ {rideEstimate.duration}
                  </p>
                </div>
                {isCalculatingRide && (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    <Loader2 className="w-5 h-5 text-blue-500" />
                  </motion.div>
                )}
              </div>
            </div>

            {/* Vehicle Options */}
            <div className="p-4 space-y-3 max-h-64 overflow-y-auto">
              {getVehicleOptions(rideEstimate.price || 15).map((vehicle) => (
                <motion.button
                  key={vehicle.id}
                  className="w-full p-4 bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 rounded-xl border border-gray-200 hover:border-blue-300 transition-all group text-left"
                  onClick={() => handleVehicleSelect(vehicle)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`p-3 bg-gradient-to-br ${vehicle.color} rounded-xl text-white shadow-lg group-hover:shadow-xl transition-shadow`}>
                        <vehicle.icon className="w-6 h-6" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-bold text-gray-900 text-base">{vehicle.name}</h4>
                          <span className="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full">
                            {vehicle.eta} min
                          </span>
                        </div>
                        <p className="text-gray-600 text-sm mt-1">{vehicle.description}</p>
                        <div className="flex items-center space-x-3 mt-2">
                          {vehicle.features.map((feature, index) => (
                            <span key={index} className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                              {feature}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center space-x-1">
                        <DollarSign className="w-4 h-4 text-green-600" />
                        <span className="text-xl font-bold text-gray-900">
                          {vehicle.price.toFixed(2)}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Preço estimado</p>
                    </div>
                  </div>
                </motion.button>
              ))}
            </div>

            {/* Footer Info */}
            <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-t border-gray-100">
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                <Clock className="w-4 h-4" />
                <span>Preços podem variar conforme demanda • Pagamento via app</span>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  )
}

export default EnhancedMapboxComponent
