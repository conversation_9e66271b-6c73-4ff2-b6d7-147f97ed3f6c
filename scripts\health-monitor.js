const SupabaseSyncManager = require('./sync-manager');
const express = require('express');
const cors = require('cors');

class HealthMonitor {
  constructor() {
    this.app = express();
    this.syncManager = new SupabaseSyncManager();
    this.port = process.env.MONITOR_PORT || 3001;
    
    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    this.app.use(cors());
    this.app.use(express.json());
    this.app.use(express.static('public'));
  }

  setupRoutes() {
    // Status da conexão
    this.app.get('/api/health', async (req, res) => {
      try {
        const status = await this.syncManager.getHealthStatus();
        res.json({
          status: 'ok',
          timestamp: new Date().toISOString(),
          supabase: status
        });
      } catch (error) {
        res.status(500).json({
          status: 'error',
          message: error.message
        });
      }
    });

    // Testar conexão
    this.app.post('/api/test-connection', async (req, res) => {
      try {
        const connected = await this.syncManager.testConnection();
        res.json({
          connected,
          message: connected ? 'Conexão OK' : 'Falha na conexão'
        });
      } catch (error) {
        res.status(500).json({
          connected: false,
          message: error.message
        });
      }
    });

    // Forçar sincronização
    this.app.post('/api/sync', async (req, res) => {
      try {
        const data = await this.syncManager.syncData();
        res.json({
          success: true,
          recordsCount: data ? data.length : 0,
          message: 'Sincronização concluída'
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          message: error.message
        });
      }
    });

    // Iniciar/parar sincronização automática
    this.app.post('/api/auto-sync/:action', (req, res) => {
      const { action } = req.params;
      
      try {
        if (action === 'start') {
          this.syncManager.startAutoSync();
          res.json({ message: 'Sincronização automática iniciada' });
        } else if (action === 'stop') {
          this.syncManager.stopAutoSync();
          res.json({ message: 'Sincronização automática parada' });
        } else {
          res.status(400).json({ message: 'Ação inválida' });
        }
      } catch (error) {
        res.status(500).json({ message: error.message });
      }
    });

    // Dashboard HTML
    this.app.get('/', (req, res) => {
      res.send(this.getDashboardHTML());
    });
  }

  getDashboardHTML() {
    return `
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>MobDrive - Monitor Supabase</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
            .header { background: #1a1a1a; color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
            .card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
            .status { display: flex; align-items: center; gap: 10px; margin-bottom: 15px; }
            .status-dot { width: 12px; height: 12px; border-radius: 50%; }
            .status-dot.online { background: #4CAF50; }
            .status-dot.offline { background: #f44336; }
            .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
            .btn-primary { background: #2196F3; color: white; }
            .btn-success { background: #4CAF50; color: white; }
            .btn-danger { background: #f44336; color: white; }
            .btn:hover { opacity: 0.8; }
            .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
            .log { background: #1a1a1a; color: #00ff00; padding: 15px; border-radius: 5px; font-family: monospace; height: 200px; overflow-y: auto; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚗 MobDrive - Monitor Supabase</h1>
                <p>Dashboard de monitoramento e sincronização</p>
            </div>
            
            <div class="grid">
                <div class="card">
                    <h3>Status da Conexão</h3>
                    <div class="status">
                        <div class="status-dot" id="statusDot"></div>
                        <span id="statusText">Verificando...</span>
                    </div>
                    <button class="btn btn-primary" onclick="testConnection()">Testar Conexão</button>
                </div>
                
                <div class="card">
                    <h3>Sincronização</h3>
                    <p>Última sincronização: <span id="lastSync">-</span></p>
                    <p>Registros: <span id="recordCount">-</span></p>
                    <button class="btn btn-success" onclick="forceSync()">Sincronizar Agora</button>
                </div>
                
                <div class="card">
                    <h3>Sincronização Automática</h3>
                    <p>Status: <span id="autoSyncStatus">Parado</span></p>
                    <button class="btn btn-success" onclick="startAutoSync()">Iniciar</button>
                    <button class="btn btn-danger" onclick="stopAutoSync()">Parar</button>
                </div>
            </div>
            
            <div class="card">
                <h3>Log de Atividades</h3>
                <div class="log" id="logContainer">
                    <div>Sistema iniciado...</div>
                </div>
            </div>
        </div>

        <script>
            let logContainer = document.getElementById('logContainer');
            
            function addLog(message) {
                const time = new Date().toLocaleTimeString();
                logContainer.innerHTML += '<div>[' + time + '] ' + message + '</div>';
                logContainer.scrollTop = logContainer.scrollHeight;
            }
            
            async function testConnection() {
                addLog('Testando conexão...');
                try {
                    const response = await fetch('/api/test-connection', { method: 'POST' });
                    const data = await response.json();
                    updateStatus(data.connected);
                    addLog(data.message);
                } catch (error) {
                    addLog('Erro: ' + error.message);
                }
            }
            
            async function forceSync() {
                addLog('Iniciando sincronização manual...');
                try {
                    const response = await fetch('/api/sync', { method: 'POST' });
                    const data = await response.json();
                    addLog(data.message + ' (' + data.recordsCount + ' registros)');
                    document.getElementById('recordCount').textContent = data.recordsCount;
                } catch (error) {
                    addLog('Erro na sincronização: ' + error.message);
                }
            }
            
            async function startAutoSync() {
                try {
                    const response = await fetch('/api/auto-sync/start', { method: 'POST' });
                    const data = await response.json();
                    addLog(data.message);
                    document.getElementById('autoSyncStatus').textContent = 'Ativo';
                } catch (error) {
                    addLog('Erro: ' + error.message);
                }
            }
            
            async function stopAutoSync() {
                try {
                    const response = await fetch('/api/auto-sync/stop', { method: 'POST' });
                    const data = await response.json();
                    addLog(data.message);
                    document.getElementById('autoSyncStatus').textContent = 'Parado';
                } catch (error) {
                    addLog('Erro: ' + error.message);
                }
            }
            
            function updateStatus(connected) {
                const dot = document.getElementById('statusDot');
                const text = document.getElementById('statusText');
                
                if (connected) {
                    dot.className = 'status-dot online';
                    text.textContent = 'Conectado';
                } else {
                    dot.className = 'status-dot offline';
                    text.textContent = 'Desconectado';
                }
            }
            
            async function checkHealth() {
                try {
                    const response = await fetch('/api/health');
                    const data = await response.json();
                    updateStatus(data.supabase.connected);
                    document.getElementById('lastSync').textContent = new Date(data.supabase.lastSync).toLocaleString();
                    if (data.supabase.totalRecords) {
                        document.getElementById('recordCount').textContent = data.supabase.totalRecords;
                    }
                } catch (error) {
                    updateStatus(false);
                }
            }
            
            // Verificar status a cada 10 segundos
            setInterval(checkHealth, 10000);
            checkHealth();
        </script>
    </body>
    </html>
    `;
  }

  async start() {
    // Inicializar sincronização
    await this.syncManager.testConnection();
    await this.syncManager.setupRealTimeSync();
    
    this.app.listen(this.port, () => {
      console.log(`🖥️ Monitor rodando em http://localhost:${this.port}`);
      console.log('📊 Dashboard disponível no navegador');
    });
  }
}

// Iniciar se executado diretamente
if (require.main === module) {
  require('dotenv').config();
  const monitor = new HealthMonitor();
  monitor.start();
}

module.exports = HealthMonitor;