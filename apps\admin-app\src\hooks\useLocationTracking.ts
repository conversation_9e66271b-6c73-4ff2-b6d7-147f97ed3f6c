import { useState, useEffect, useCallback } from 'react'
import { locationService, UserLocation } from '../services/LocationService'
import { useAuth } from '../contexts/AuthContextSimple';

interface LocationTrackingState {
  isTracking: boolean
  currentLocation: UserLocation | null
  locationHistory: UserLocation[]
  error: string | null
  loading: boolean
}

export const useLocationTracking = () => {
  const { user } = useAuth()
  const [state, setState] = useState<LocationTrackingState>({
    isTracking: false,
    currentLocation: null,
    locationHistory: [],
    error: null,
    loading: false
  })

  // Iniciar tracking
  const startTracking = useCallback(async () => {
    if (!user) {
      setState(prev => ({ ...prev, error: 'Usuário não autenticado' }))
      return
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }))
      await locationService.startLocationTracking(user.id)
      setState(prev => ({ 
        ...prev, 
        isTracking: true, 
        loading: false,
        error: null 
      }))
      console.log('✅ Location tracking iniciado via hook')
    } catch (error: any) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error.message,
        isTracking: false 
      }))
      console.error('❌ Erro ao iniciar tracking:', error)
    }
  }, [user])

  // Parar tracking
  const stopTracking = useCallback(async () => {
    if (!user) return

    try {
      setState(prev => ({ ...prev, loading: true }))
      await locationService.stopLocationTracking(user.id)
      setState(prev => ({ 
        ...prev, 
        isTracking: false, 
        loading: false,
        error: null 
      }))
      console.log('✅ Location tracking parado via hook')
    } catch (error: any) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error.message 
      }))
      console.error('❌ Erro ao parar tracking:', error)
    }
  }, [user])

  // Obter localização atual
  const getCurrentLocation = useCallback(async () => {
    if (!user) return

    try {
      setState(prev => ({ ...prev, loading: true }))
      const location = await locationService.getCurrentUserLocation(user.id)
      setState(prev => ({ 
        ...prev, 
        currentLocation: location, 
        loading: false,
        error: null 
      }))
      return location
    } catch (error: any) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error.message 
      }))
      console.error('❌ Erro ao obter localização:', error)
      return null
    }
  }, [user])

  // Obter histórico
  const getLocationHistory = useCallback(async (limit = 50) => {
    if (!user) return []

    try {
      setState(prev => ({ ...prev, loading: true }))
      const history = await locationService.getLocationHistory(user.id, limit)
      setState(prev => ({ 
        ...prev, 
        locationHistory: history, 
        loading: false,
        error: null 
      }))
      return history
    } catch (error: any) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error.message 
      }))
      console.error('❌ Erro ao obter histórico:', error)
      return []
    }
  }, [user])

  // Verificar status do tracking
  const checkTrackingStatus = useCallback(() => {
    const isTracking = locationService.isLocationTracking()
    setState(prev => ({ ...prev, isTracking }))
    return isTracking
  }, [])

  // Atualizar localização atual periodicamente
  useEffect(() => {
    if (!user || !state.isTracking) return

    const interval = setInterval(async () => {
      await getCurrentLocation()
    }, 60000) // Atualizar a cada minuto

    return () => clearInterval(interval)
  }, [user, state.isTracking, getCurrentLocation])

  // Verificar status inicial
  useEffect(() => {
    checkTrackingStatus()
  }, [checkTrackingStatus])

  return {
    ...state,
    startTracking,
    stopTracking,
    getCurrentLocation,
    getLocationHistory,
    checkTrackingStatus,
    // Helpers
    hasLocation: !!state.currentLocation,
    isLocationAvailable: state.currentLocation !== null,
    lastLocationUpdate: state.currentLocation?.timestamp,
    locationAccuracy: state.currentLocation?.accuracy,
    locationAddress: state.currentLocation?.address
  }
}

export default useLocationTracking
