const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

class SupabaseSyncManager {
  constructor() {
    this.supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_ANON_KEY
    );
    this.isConnected = false;
    this.syncInterval = null;
    this.retryCount = 0;
    this.maxRetries = 5;
  }

  async testConnection() {
    try {
      console.log('🔄 Testando conexão com Supabase...');
      const { data, error } = await this.supabase
        .from('corridas')
        .select('count')
        .limit(1);

      if (error) throw error;

      this.isConnected = true;
      console.log('✅ Conexão com Supabase estabelecida com sucesso!');
      return true;
    } catch (error) {
      this.isConnected = false;
      console.error('❌ Erro na conexão com Supabase:', error.message);
      return false;
    }
  }

  async syncData() {
    if (!this.isConnected) {
      console.log('⚠️ Tentando reconectar...');
      const connected = await this.testConnection();
      if (!connected) {
        this.retryCount++;
        if (this.retryCount < this.maxRetries) {
          setTimeout(() => this.syncData(), 5000);
        }
        return;
      }
      this.retryCount = 0;
    }

    try {
      // Sincronizar dados das corridas
      const { data: corridas, error } = await this.supabase
        .from('corridas')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;

      console.log(`📊 Sincronizados ${corridas.length} registros de corridas`);

      // Salvar backup local
      if (process.env.ENABLE_AUTO_BACKUP === 'true') {
        await this.createBackup(corridas);
      }

      return corridas;
    } catch (error) {
      console.error('❌ Erro na sincronização:', error.message);
      this.isConnected = false;
    }
  }

  async createBackup(data) {
    try {
      const backupDir = path.join(__dirname, '../backups');
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFile = path.join(backupDir, `backup-${timestamp}.json`);

      fs.writeFileSync(backupFile, JSON.stringify(data, null, 2));
      console.log(`💾 Backup criado: ${backupFile}`);
    } catch (error) {
      console.error('❌ Erro ao criar backup:', error.message);
    }
  }

  startAutoSync() {
    const interval = parseInt(process.env.SYNC_INTERVAL) || 30000;

    console.log(`🔄 Iniciando sincronização automática (${interval}ms)`);

    this.syncInterval = setInterval(() => {
      this.syncData();
    }, interval);
  }

  stopAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      console.log('⏹️ Sincronização automática parada');
    }
  }

  async setupRealTimeSync() {
    if (process.env.ENABLE_REAL_TIME !== 'true') return;

    try {
      const channel = this.supabase
        .channel('corridas-changes')
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'corridas' },
          (payload) => {
            console.log('🔄 Mudança detectada:', payload);
            this.handleRealTimeChange(payload);
          }
        )
        .subscribe();

      console.log('🔴 Sincronização em tempo real ativada');
      return channel;
    } catch (error) {
      console.error('❌ Erro ao configurar tempo real:', error.message);
    }
  }

  handleRealTimeChange(payload) {
    const { eventType, new: newRecord, old: oldRecord } = payload;

    switch (eventType) {
      case 'INSERT':
        console.log('➕ Nova corrida adicionada:', newRecord.id);
        break;
      case 'UPDATE':
        console.log('✏️ Corrida atualizada:', newRecord.id);
        break;
      case 'DELETE':
        console.log('🗑️ Corrida removida:', oldRecord.id);
        break;
    }
  }

  async getHealthStatus() {
    const status = {
      connected: this.isConnected,
      lastSync: new Date().toISOString(),
      retryCount: this.retryCount,
      autoSyncActive: !!this.syncInterval
    };

    try {
      const { data, error } = await this.supabase
        .from('corridas')
        .select('count');

      if (!error) {
        status.totalRecords = data.length;
      }
    } catch (error) {
      status.error = error.message;
    }

    return status;
  }
}

module.exports = SupabaseSyncManager;