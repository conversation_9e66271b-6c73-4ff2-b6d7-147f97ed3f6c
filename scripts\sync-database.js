#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Configuração
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://udquhavmgqtpkubrfzdm.supabase.co'
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!SUPABASE_SERVICE_KEY) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY não encontrada nas variáveis de ambiente')
  process.exit(1)
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY)

class DatabaseSyncer {
  constructor() {
    this.migrationsPath = path.join(__dirname, '../database/migrations')
    this.appliedMigrations = new Set()
  }

  async init() {
    console.log('🚀 Iniciando sincronização do banco de dados...')

    // Criar tabela de controle de migrações se não existir
    await this.createMigrationsTable()

    // Carregar migrações já aplicadas
    await this.loadAppliedMigrations()

    // Aplicar novas migrações
    await this.applyMigrations()

    console.log('✅ Sincronização concluída!')
  }

  async createMigrationsTable() {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS _migrations (
          id SERIAL PRIMARY KEY,
          filename VARCHAR(255) UNIQUE NOT NULL,
          applied_at TIMESTAMPTZ DEFAULT NOW(),
          checksum VARCHAR(64)
        );
      `
    })

    if (error) {
      console.error('❌ Erro ao criar tabela de migrações:', error)
      throw error
    }
  }

  async loadAppliedMigrations() {
    const { data, error } = await supabase
      .from('_migrations')
      .select('filename')

    if (error) {
      console.error('❌ Erro ao carregar migrações aplicadas:', error)
      return
    }

    data?.forEach(migration => {
      this.appliedMigrations.add(migration.filename)
    })

    console.log(`📋 ${this.appliedMigrations.size} migrações já aplicadas`)
  }

  async applyMigrations() {
    if (!fs.existsSync(this.migrationsPath)) {
      console.log('📁 Pasta de migrações não encontrada')
      return
    }

    const files = fs.readdirSync(this.migrationsPath)
      .filter(file => file.endsWith('.sql'))
      .sort()

    console.log(`📄 ${files.length} arquivos de migração encontrados`)

    for (const file of files) {
      if (this.appliedMigrations.has(file)) {
        console.log(`⏭️  Pulando ${file} (já aplicada)`)
        continue
      }

      await this.applyMigration(file)
    }
  }

  async applyMigration(filename) {
    console.log(`🔄 Aplicando migração: ${filename}`)

    const filePath = path.join(this.migrationsPath, filename)
    const sql = fs.readFileSync(filePath, 'utf8')

    try {
      // Executar a migração
      const { error } = await supabase.rpc('exec_sql', { sql })

      if (error) {
        console.error(`❌ Erro ao aplicar ${filename}:`, error)
        throw error
      }

      // Registrar migração como aplicada
      const { error: insertError } = await supabase
        .from('_migrations')
        .insert({
          filename,
          checksum: this.generateChecksum(sql)
        })

      if (insertError) {
        console.error(`❌ Erro ao registrar migração ${filename}:`, insertError)
        throw insertError
      }

      console.log(`✅ Migração ${filename} aplicada com sucesso`)

    } catch (error) {
      console.error(`❌ Falha ao aplicar migração ${filename}:`, error)
      throw error
    }
  }

  generateChecksum(content) {
    // Simples hash para verificar integridade
    return Buffer.from(content).toString('base64').slice(0, 64)
  }

  async testConnection() {
    console.log('🔍 Testando conexão com o banco...')

    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1)

    if (error) {
      console.error('❌ Erro na conexão:', error)
      return false
    }

    console.log('✅ Conexão estabelecida com sucesso')
    return true
  }

  async validateSchema() {
    console.log('🔍 Validando esquema do banco...')

    const tables = ['profiles', 'drivers', 'rides', 'ride_requests', 'driver_locations']
    const results = []

    for (const table of tables) {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1)

      results.push({
        table,
        exists: !error,
        error: error?.message
      })
    }

    console.log('📊 Resultado da validação:')
    results.forEach(({ table, exists, error }) => {
      if (exists) {
        console.log(`  ✅ ${table}`)
      } else {
        console.log(`  ❌ ${table}: ${error}`)
      }
    })

    return results
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const syncer = new DatabaseSyncer()

  syncer.init()
    .then(() => {
      console.log('🎉 Processo de sincronização finalizado!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Erro durante a sincronização:', error)
      process.exit(1)
    })
}

export default DatabaseSyncer