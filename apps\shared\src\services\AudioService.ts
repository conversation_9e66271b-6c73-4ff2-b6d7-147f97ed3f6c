/**
 * Serviço de Áudio Compartilhado para MobiDrive
 * 
 * Este serviço gerencia a reprodução de áudio em todos os aplicativos MobiDrive,
 * incluindo sons de notificação, alertas e feedback de interface.
 */

export type NotificationSoundType = 'default' | 'urgent' | 'success' | 'warning' | 'error' | 'message' | 'payment';

interface AudioServiceOptions {
  debug?: boolean;
  defaultVolume?: number;
  defaultMuted?: boolean;
  storagePrefix?: string;
}

class AudioService {
  private static instance: AudioService;
  private hasUserInteraction: boolean = false;
  private audioContext: AudioContext | null = null;
  private audioCache: Map<string, HTMLAudioElement> = new Map();
  private volume: number = 1.0;
  private isMuted: boolean = false;
  private debug: boolean = false;
  private storagePrefix: string = 'mobdrive-';

  // Mapeamento de tipos de notificação para sons
  private readonly notificationSounds: Record<NotificationSoundType, string> = {
    default: '/sounds/notification.mp3',
    urgent: '/sounds/urgent.mp3',
    success: '/sounds/success.mp3',
    warning: '/sounds/warning.mp3',
    error: '/sounds/error.mp3',
    message: '/sounds/message.mp3',
    payment: '/sounds/payment.mp3'
  };

  private constructor(options?: AudioServiceOptions) {
    // Aplicar opções
    if (options) {
      this.debug = options.debug || false;
      this.volume = options.defaultVolume !== undefined ? options.defaultVolume : 1.0;
      this.isMuted = options.defaultMuted || false;
      this.storagePrefix = options.storagePrefix || 'mobdrive-';
    }

    // Adicionar listeners para detectar interação do usuário
    if (typeof window !== 'undefined') {
      const interactionEvents = ['click', 'touchstart', 'keydown'];
      const handleUserInteraction = () => {
        this.hasUserInteraction = true;
        this.initAudioContext();

        // Remover os listeners após a primeira interação
        interactionEvents.forEach(event => {
          window.removeEventListener(event, handleUserInteraction);
        });
      };

      // Adicionar listeners para eventos de interação
      interactionEvents.forEach(event => {
        window.addEventListener(event, handleUserInteraction);
      });

      // Carregar configurações salvas
      this.loadSettings();
    }
  }

  private log(level: 'debug' | 'info' | 'warn' | 'error', message: string, data?: any): void {
    if (!this.debug && level === 'debug') return;

    const prefix = '[AudioService]';
    
    switch (level) {
      case 'debug':
        console.debug(`${prefix} ${message}`, data);
        break;
      case 'info':
        console.info(`${prefix} ${message}`, data);
        break;
      case 'warn':
        console.warn(`${prefix} ${message}`, data);
        break;
      case 'error':
        console.error(`${prefix} ${message}`, data);
        break;
    }
  }

  private loadSettings(): void {
    try {
      const savedVolume = localStorage.getItem(`${this.storagePrefix}audioVolume`);
      const savedMuted = localStorage.getItem(`${this.storagePrefix}audioMuted`);
      
      if (savedVolume) {
        this.volume = parseFloat(savedVolume);
      }
      if (savedMuted) {
        this.isMuted = savedMuted === 'true';
      }
    } catch (error) {
      this.log('error', 'Erro ao carregar configurações de áudio', error);
    }
  }

  private saveSettings(): void {
    try {
      localStorage.setItem(`${this.storagePrefix}audioVolume`, this.volume.toString());
      localStorage.setItem(`${this.storagePrefix}audioMuted`, this.isMuted.toString());
    } catch (error) {
      this.log('error', 'Erro ao salvar configurações de áudio', error);
    }
  }

  public setVolume(volume: number): void {
    this.volume = Math.max(0, Math.min(1, volume));
    this.saveSettings();
    
    // Atualizar volume de todos os áudios em cache
    this.audioCache.forEach(audio => {
      audio.volume = this.volume;
    });
  }

  public getVolume(): number {
    return this.volume;
  }

  public toggleMute(): void {
    this.isMuted = !this.isMuted;
    this.saveSettings();
    
    // Atualizar mute de todos os áudios em cache
    this.audioCache.forEach(audio => {
      audio.muted = this.isMuted;
    });
  }

  public isMutedState(): boolean {
    return this.isMuted;
  }

  public static getInstance(options?: AudioServiceOptions): AudioService {
    if (!AudioService.instance) {
      AudioService.instance = new AudioService(options);
    }
    return AudioService.instance;
  }

  /**
   * Inicializar AudioContext
   */
  private initAudioContext(): void {
    if (!this.audioContext && typeof window !== 'undefined' && 'AudioContext' in window) {
      try {
        this.audioContext = new AudioContext();
        this.log('debug', 'AudioContext inicializado');
      } catch (error) {
        this.log('warn', 'Erro ao inicializar AudioContext', error);
      }
    }
  }

  /**
   * Reproduzir um som
   */
  public async playSound(url: string): Promise<void> {
    try {
      // Verificar se o navegador está em primeiro plano
      if (document.visibilityState !== 'visible') {
        this.log('debug', 'Som ignorado (página em segundo plano)', { url });
        return;
      }

      // Se não houver interação do usuário, logar aviso e retornar
      if (!this.hasUserInteraction) {
        this.log('warn', 'Tentativa de reproduzir som sem interação do usuário', { url });
        return;
      }

      // Se estiver mudo, não reproduzir
      if (this.isMuted) {
        this.log('debug', 'Som ignorado (áudio mudo)', { url });
        return;
      }

      // Verificar se o som já está em cache
      let audio = this.audioCache.get(url);

      // Se não estiver em cache, criar novo elemento de áudio
      if (!audio) {
        audio = new Audio(url);
        audio.preload = 'auto';
        audio.volume = this.volume;
        audio.muted = this.isMuted;

        // Adicionar ao cache
        this.audioCache.set(url, audio);
      }

      // Tentar reproduzir o som
      await audio.play();
      this.log('debug', 'Som reproduzido com sucesso', { url });
    } catch (error) {
      this.log('error', 'Erro ao reproduzir som', { error, url });
    }
  }

  /**
   * Reproduzir som de notificação
   */
  public async playNotificationSound(type: NotificationSoundType = 'default'): Promise<void> {
    const soundUrl = this.notificationSounds[type];
    if (!soundUrl) {
      this.log('error', 'Tipo de notificação inválido', { type });
      return;
    }

    await this.playSound(soundUrl);
  }

  /**
   * Pré-carregar todos os sons de notificação
   */
  public async preloadAllNotificationSounds(): Promise<void> {
    const preloadPromises = Object.values(this.notificationSounds).map(url => 
      this.preloadSound(url).catch(error => {
        this.log('error', 'Erro ao pré-carregar som', { error, url });
      })
    );

    await Promise.all(preloadPromises);
    this.log('info', 'Todos os sons de notificação foram pré-carregados');
  }

  /**
   * Pré-carregar um arquivo de áudio para uso posterior
   */
  public async preloadSound(url: string): Promise<void> {
    try {
      // Se já estiver em cache, não precisa carregar novamente
      if (this.audioCache.has(url)) {
        return;
      }

      // Criar novo elemento de áudio
      const audio = new Audio(url);
      audio.preload = 'auto';
      audio.volume = this.volume;
      audio.muted = this.isMuted;

      // Adicionar ao cache
      this.audioCache.set(url, audio);
      this.log('debug', 'Áudio pré-carregado com sucesso', { url });
    } catch (error) {
      this.log('error', 'Erro ao pré-carregar som', { error, url });
    }
  }

  /**
   * Limpar cache de áudio
   */
  public clearCache(): void {
    this.audioCache.clear();
    this.log('debug', 'Cache de áudio limpo');
  }
}

// Exportar função para criar instância
export const createAudioService = (options?: AudioServiceOptions) => {
  return AudioService.getInstance(options);
};

// Exportar instância padrão
const defaultAudioService = AudioService.getInstance();
export default defaultAudioService;
