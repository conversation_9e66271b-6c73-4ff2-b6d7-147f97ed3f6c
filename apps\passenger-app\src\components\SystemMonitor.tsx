import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Activity, Wifi, Database, MapPin, Bell, Zap, AlertTriangle, CheckCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'
import { analyticsService } from '../services/AnalyticsService'
import { notificationService } from '../services/NotificationService'
import { cacheService } from '../services/CacheService'

interface SystemStatus {
  supabase: 'online' | 'offline' | 'slow'
  mapbox: 'online' | 'offline' | 'slow'
  notifications: 'enabled' | 'disabled' | 'blocked'
  cache: 'active' | 'inactive'
  analytics: 'tracking' | 'paused'
  realtime: 'connected' | 'disconnected' | 'reconnecting'
}

interface PerformanceMetrics {
  loadTime: number
  memoryUsage: number
  cacheHitRate: number
  apiLatency: number
  errorRate: number
}

export const SystemMonitor: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false)
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    supabase: 'online',
    mapbox: 'online',
    notifications: 'enabled',
    cache: 'active',
    analytics: 'tracking',
    realtime: 'connected'
  })
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    memoryUsage: 0,
    cacheHitRate: 0,
    apiLatency: 0,
    errorRate: 0
  })
  const [logs, setLogs] = useState<string[]>([])

  // Monitorar status do sistema
  useEffect(() => {
    const checkSystemStatus = async () => {
      try {
        // Verificar Supabase
        const startTime = Date.now()
        const { error: supabaseError } = await supabase.from('ride_requests').select('id').limit(1)
        const supabaseLatency = Date.now() - startTime

        setSystemStatus(prev => ({
          ...prev,
          supabase: supabaseError ? 'offline' : supabaseLatency > 2000 ? 'slow' : 'online'
        }))

        // Verificar Mapbox com query válida
        try {
          const mapboxToken = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN
          if (mapboxToken) {
            const mapboxResponse = await fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/São Paulo.json?access_token=${mapboxToken}`)
            setSystemStatus(prev => ({
              ...prev,
              mapbox: mapboxResponse.ok ? 'online' : 'offline'
            }))
          } else {
            setSystemStatus(prev => ({ ...prev, mapbox: 'offline' }))
          }
        } catch {
          setSystemStatus(prev => ({ ...prev, mapbox: 'offline' }))
        }

        // Verificar notificações
        const notificationStatus = notificationService.getStatus()
        setSystemStatus(prev => ({
          ...prev,
          notifications: notificationStatus.permission === 'granted' ? 'enabled' : 
                        notificationStatus.permission === 'denied' ? 'blocked' : 'disabled'
        }))

        // Verificar cache
        const cacheStats = cacheService.getStats()
        setSystemStatus(prev => ({
          ...prev,
          cache: cacheStats.totalItems > 0 ? 'active' : 'inactive'
        }))

        // Verificar analytics
        const analyticsStats = analyticsService.getSessionStats()
        setSystemStatus(prev => ({
          ...prev,
          analytics: analyticsStats.isOnline ? 'tracking' : 'paused'
        }))

        // Atualizar métricas
        setMetrics(prev => ({
          ...prev,
          apiLatency: supabaseLatency,
          cacheHitRate: cacheStats.cacheHitRate || 0
        }))

        addLog(`Sistema verificado - Supabase: ${supabaseLatency}ms`)
      } catch (error) {
        addLog(`Erro na verificação: ${error}`)
      }
    }

    // Verificar a cada 30 segundos
    const interval = setInterval(checkSystemStatus, 30000)
    checkSystemStatus() // Verificação inicial

    return () => clearInterval(interval)
  }, [])

  // Monitorar performance
  useEffect(() => {
    const updatePerformanceMetrics = () => {
      // Tempo de carregamento
      if (performance.timing) {
        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart
        setMetrics(prev => ({ ...prev, loadTime }))
      }

      // Uso de memória
      if ('memory' in performance) {
        const memory = (performance as any).memory
        const memoryUsage = (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
        setMetrics(prev => ({ ...prev, memoryUsage }))
      }
    }

    updatePerformanceMetrics()
    const interval = setInterval(updatePerformanceMetrics, 10000)

    return () => clearInterval(interval)
  }, [])

  // Adicionar log
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)])
  }

  // Obter cor do status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
      case 'enabled':
      case 'active':
      case 'tracking':
      case 'connected':
        return 'text-green-500'
      case 'slow':
      case 'disabled':
      case 'reconnecting':
        return 'text-yellow-500'
      case 'offline':
      case 'blocked':
      case 'inactive':
      case 'paused':
      case 'disconnected':
        return 'text-red-500'
      default:
        return 'text-gray-500'
    }
  }

  // Obter ícone do status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
      case 'enabled':
      case 'active':
      case 'tracking':
      case 'connected':
        return <CheckCircle className="w-4 h-4" />
      case 'slow':
      case 'disabled':
      case 'reconnecting':
        return <AlertTriangle className="w-4 h-4" />
      default:
        return <AlertTriangle className="w-4 h-4" />
    }
  }

  // Ações de diagnóstico
  const runDiagnostics = async () => {
    addLog('Iniciando diagnósticos...')
    
    // Limpar cache
    cacheService.clear()
    addLog('Cache limpo')

    // Forçar flush de analytics
    await analyticsService.flush()
    addLog('Analytics sincronizado')

    // Testar conectividade
    try {
      await supabase.from('ride_requests').select('id').limit(1)
      addLog('Conectividade Supabase: OK')
    } catch (error) {
      addLog('Erro na conectividade Supabase')
    }

    addLog('Diagnósticos concluídos')
  }

  if (!isVisible) {
    return (
      <motion.button
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 left-4 z-50 w-12 h-12 bg-gray-800 hover:bg-gray-700 text-white rounded-full shadow-lg flex items-center justify-center"
      >
        <Activity className="w-5 h-5" />
      </motion.button>
    )
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, x: -300 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -300 }}
        className="fixed bottom-4 left-4 z-50 w-80 bg-gray-900 text-white rounded-2xl shadow-2xl overflow-hidden"
      >
        {/* Header */}
        <div className="bg-gray-800 p-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Activity className="w-5 h-5 text-blue-400" />
            <h3 className="font-semibold">System Monitor</h3>
          </div>
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-400 hover:text-white"
          >
            ×
          </button>
        </div>

        {/* Status Grid */}
        <div className="p-4 space-y-3">
          <div className="grid grid-cols-2 gap-3">
            {/* Supabase */}
            <div className="flex items-center space-x-2">
              <Database className="w-4 h-4 text-blue-400" />
              <span className="text-sm">Supabase</span>
              <div className={`ml-auto flex items-center space-x-1 ${getStatusColor(systemStatus.supabase)}`}>
                {getStatusIcon(systemStatus.supabase)}
                <span className="text-xs">{systemStatus.supabase}</span>
              </div>
            </div>

            {/* Mapbox */}
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-green-400" />
              <span className="text-sm">Mapbox</span>
              <div className={`ml-auto flex items-center space-x-1 ${getStatusColor(systemStatus.mapbox)}`}>
                {getStatusIcon(systemStatus.mapbox)}
                <span className="text-xs">{systemStatus.mapbox}</span>
              </div>
            </div>

            {/* Notifications */}
            <div className="flex items-center space-x-2">
              <Bell className="w-4 h-4 text-purple-400" />
              <span className="text-sm">Notifications</span>
              <div className={`ml-auto flex items-center space-x-1 ${getStatusColor(systemStatus.notifications)}`}>
                {getStatusIcon(systemStatus.notifications)}
                <span className="text-xs">{systemStatus.notifications}</span>
              </div>
            </div>

            {/* Cache */}
            <div className="flex items-center space-x-2">
              <Zap className="w-4 h-4 text-yellow-400" />
              <span className="text-sm">Cache</span>
              <div className={`ml-auto flex items-center space-x-1 ${getStatusColor(systemStatus.cache)}`}>
                {getStatusIcon(systemStatus.cache)}
                <span className="text-xs">{systemStatus.cache}</span>
              </div>
            </div>
          </div>

          {/* Metrics */}
          <div className="border-t border-gray-700 pt-3">
            <h4 className="text-sm font-medium mb-2">Performance</h4>
            <div className="space-y-2 text-xs">
              <div className="flex justify-between">
                <span>Load Time:</span>
                <span>{metrics.loadTime}ms</span>
              </div>
              <div className="flex justify-between">
                <span>Memory:</span>
                <span>{metrics.memoryUsage.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between">
                <span>API Latency:</span>
                <span>{metrics.apiLatency}ms</span>
              </div>
              <div className="flex justify-between">
                <span>Cache Hit:</span>
                <span>{metrics.cacheHitRate.toFixed(1)}%</span>
              </div>
            </div>
          </div>

          {/* Logs */}
          <div className="border-t border-gray-700 pt-3">
            <h4 className="text-sm font-medium mb-2">System Logs</h4>
            <div className="bg-gray-800 rounded p-2 max-h-32 overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index} className="text-xs text-gray-300 mb-1">
                  {log}
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="border-t border-gray-700 pt-3">
            <button
              onClick={runDiagnostics}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm py-2 px-3 rounded-lg transition-colors"
            >
              Run Diagnostics
            </button>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}

export default SystemMonitor
